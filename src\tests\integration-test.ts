// Integration Test Suite for ChefAI Extension
import { LLMService } from '../services/LLMService';
import { AdvancedSettingsService } from '../services/AdvancedSettingsService';
import { RecipeService } from '../services/RecipeService';
import { RealtimeUpdateService } from '../services/RealtimeUpdateService';
import { CustomizationService } from '../services/CustomizationService';
import { APIManager } from '../services/APIManager';

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  duration: number;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  totalPassed: number;
  totalFailed: number;
  totalDuration: number;
}

export class ChefAIIntegrationTest {
  private testResults: TestSuite[] = [];
  private services: {
    llm: LLMService;
    settings: AdvancedSettingsService;
    recipes: RecipeService;
    realtime: RealtimeUpdateService;
    customization: CustomizationService;
    api: APIManager;
  };

  constructor() {
    this.services = {
      llm: LLMService.getInstance(),
      settings: AdvancedSettingsService.getInstance(),
      recipes: RecipeService.getInstance(),
      realtime: RealtimeUpdateService.getInstance(),
      customization: CustomizationService.getInstance(),
      api: APIManager.getInstance()
    };
  }

  public async runAllTests(): Promise<TestSuite[]> {
    console.log('🧪 Starting ChefAI Integration Tests...');
    
    this.testResults = [];

    // Run test suites
    await this.testServiceInitialization();
    await this.testLLMService();
    await this.testAdvancedSettings();
    await this.testRecipeService();
    await this.testRealtimeUpdates();
    await this.testCustomizationService();
    await this.testAPIManager();
    await this.testIntegration();

    this.printTestSummary();
    return this.testResults;
  }

  private async runTest(
    suiteName: string,
    testName: string,
    testFunction: () => Promise<void>
  ): Promise<void> {
    const startTime = Date.now();
    let suite = this.testResults.find(s => s.name === suiteName);
    
    if (!suite) {
      suite = {
        name: suiteName,
        tests: [],
        totalPassed: 0,
        totalFailed: 0,
        totalDuration: 0
      };
      this.testResults.push(suite);
    }

    try {
      await testFunction();
      const duration = Date.now() - startTime;
      
      suite.tests.push({
        testName,
        passed: true,
        duration
      });
      suite.totalPassed++;
      suite.totalDuration += duration;
      
      console.log(`✅ ${suiteName}: ${testName} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      
      suite.tests.push({
        testName,
        passed: false,
        error: error.message,
        duration
      });
      suite.totalFailed++;
      suite.totalDuration += duration;
      
      console.error(`❌ ${suiteName}: ${testName} - ${error.message} (${duration}ms)`);
    }
  }

  private async testServiceInitialization(): Promise<void> {
    await this.runTest('Service Initialization', 'LLM Service Instance', async () => {
      if (!this.services.llm) throw new Error('LLM Service not initialized');
    });

    await this.runTest('Service Initialization', 'Advanced Settings Service Instance', async () => {
      if (!this.services.settings) throw new Error('Advanced Settings Service not initialized');
    });

    await this.runTest('Service Initialization', 'Recipe Service Instance', async () => {
      if (!this.services.recipes) throw new Error('Recipe Service not initialized');
    });

    await this.runTest('Service Initialization', 'Realtime Update Service Instance', async () => {
      if (!this.services.realtime) throw new Error('Realtime Update Service not initialized');
    });

    await this.runTest('Service Initialization', 'Customization Service Instance', async () => {
      if (!this.services.customization) throw new Error('Customization Service not initialized');
    });

    await this.runTest('Service Initialization', 'API Manager Instance', async () => {
      if (!this.services.api) throw new Error('API Manager not initialized');
    });
  }

  private async testLLMService(): Promise<void> {
    await this.runTest('LLM Service', 'Get Providers', async () => {
      const providers = this.services.llm.getProviders();
      if (!Array.isArray(providers) || providers.length === 0) {
        throw new Error('No LLM providers available');
      }
    });

    await this.runTest('LLM Service', 'Configuration Management', async () => {
      const config = this.services.llm.getConfiguration();
      if (!config) throw new Error('No LLM configuration available');
    });

    await this.runTest('LLM Service', 'Provider Selection', async () => {
      const providers = this.services.llm.getProviders();
      const firstProvider = providers[0];
      
      if (!firstProvider.models || firstProvider.models.length === 0) {
        throw new Error('Provider has no models');
      }
    });
  }

  private async testAdvancedSettings(): Promise<void> {
    await this.runTest('Advanced Settings', 'Load Settings', async () => {
      const settings = await this.services.settings.loadSettings();
      if (!settings) throw new Error('Failed to load settings');
    });

    await this.runTest('Advanced Settings', 'Update Setting', async () => {
      await this.services.settings.updateSetting('creativityLevel', 75);
      const settings = this.services.settings.getSettings();
      if (settings.creativityLevel !== 75) {
        throw new Error('Setting not updated correctly');
      }
    });

    await this.runTest('Advanced Settings', 'Generate System Prompt', async () => {
      const prompt = this.services.settings.generateSystemPrompt();
      if (!prompt || prompt.length < 50) {
        throw new Error('System prompt too short or empty');
      }
    });

    await this.runTest('Advanced Settings', 'Export/Import Settings', async () => {
      const exported = this.services.settings.exportSettings();
      const success = await this.services.settings.importSettings(exported);
      if (!success) throw new Error('Failed to import settings');
    });
  }

  private async testRecipeService(): Promise<void> {
    const testRecipe = {
      id: 'test-recipe-' + Date.now(),
      title: 'Test Recipe',
      description: 'A test recipe for integration testing',
      ingredients: [
        { name: 'Test Ingredient', amount: '1', unit: 'cup' }
      ],
      steps: [
        { instruction: 'Test step 1', duration: 5 }
      ],
      cookTime: 30,
      servings: 4,
      difficulty: 'easy' as const,
      cuisine: 'test',
      source: 'AI Generated',
      tags: ['test'],
      createdAt: new Date()
    };

    await this.runTest('Recipe Service', 'Save Recipe', async () => {
      await this.services.recipes.saveRecipe(testRecipe);
    });

    await this.runTest('Recipe Service', 'Get Recipe by ID', async () => {
      const retrieved = await this.services.recipes.getRecipeById(testRecipe.id);
      if (!retrieved || retrieved.id !== testRecipe.id) {
        throw new Error('Failed to retrieve saved recipe');
      }
    });

    await this.runTest('Recipe Service', 'Search Recipes', async () => {
      const results = await this.services.recipes.getRecipes({
        query: 'test',
        limit: 10
      });
      if (!Array.isArray(results)) {
        throw new Error('Search results not an array');
      }
    });

    await this.runTest('Recipe Service', 'Add to Favorites', async () => {
      await this.services.recipes.addToFavorites(testRecipe.id);
      const favorites = await this.services.recipes.getFavorites();
      if (!favorites.some(r => r.id === testRecipe.id)) {
        throw new Error('Recipe not added to favorites');
      }
    });

    await this.runTest('Recipe Service', 'Rate Recipe', async () => {
      await this.services.recipes.rateRecipe(testRecipe.id, 5);
      const rating = await this.services.recipes.getRecipeRating(testRecipe.id);
      if (rating !== 5) {
        throw new Error('Recipe rating not saved correctly');
      }
    });

    // Cleanup
    await this.runTest('Recipe Service', 'Delete Recipe', async () => {
      await this.services.recipes.deleteRecipe(testRecipe.id);
      const deleted = await this.services.recipes.getRecipeById(testRecipe.id);
      if (deleted) {
        throw new Error('Recipe not deleted');
      }
    });
  }

  private async testRealtimeUpdates(): Promise<void> {
    let eventReceived = false;
    let receivedEvent: any = null;

    await this.runTest('Realtime Updates', 'Subscribe to Events', async () => {
      this.services.realtime.subscribe(
        'test-listener',
        ['test_event'],
        (event) => {
          eventReceived = true;
          receivedEvent = event;
        }
      );
    });

    await this.runTest('Realtime Updates', 'Emit Event', async () => {
      this.services.realtime.emit('test_event', { test: 'data' }, 'test-source');
      
      // Wait a bit for event processing
      await new Promise(resolve => setTimeout(resolve, 100));
      
      if (!eventReceived) {
        throw new Error('Event not received by listener');
      }
      
      if (!receivedEvent || receivedEvent.data.test !== 'data') {
        throw new Error('Event data incorrect');
      }
    });

    await this.runTest('Realtime Updates', 'Event History', async () => {
      const history = this.services.realtime.getEventHistory('test_event');
      if (history.length === 0) {
        throw new Error('Event not in history');
      }
    });

    await this.runTest('Realtime Updates', 'Unsubscribe', async () => {
      this.services.realtime.unsubscribe('test-listener');
      const listenerCount = this.services.realtime.getListenerCount();
      // Should be 0 or at least one less than before
    });
  }

  private async testCustomizationService(): Promise<void> {
    await this.runTest('Customization Service', 'Load Profiles', async () => {
      await this.services.customization.loadProfiles();
      const profiles = this.services.customization.getAllProfiles();
      if (profiles.length === 0) {
        throw new Error('No customization profiles loaded');
      }
    });

    await this.runTest('Customization Service', 'Create Profile', async () => {
      const profile = await this.services.customization.createProfile(
        'Test Profile',
        'A test customization profile'
      );
      if (!profile || !profile.id) {
        throw new Error('Failed to create profile');
      }
    });

    await this.runTest('Customization Service', 'Get Current Profile', async () => {
      const current = this.services.customization.getCurrentProfile();
      if (!current) {
        throw new Error('No current profile set');
      }
    });
  }

  private async testAPIManager(): Promise<void> {
    const testConfig = {
      baseURL: 'https://httpbin.org',
      apiKey: 'test-key',
      timeout: 5000,
      retryAttempts: 1,
      retryDelay: 1000
    };

    await this.runTest('API Manager', 'Make Request', async () => {
      const response = await this.services.api.makeRequest(
        'test-api',
        testConfig,
        {
          method: 'GET',
          endpoint: '/get'
        }
      );
      
      if (!response.success) {
        throw new Error(`API request failed: ${response.error}`);
      }
    });

    await this.runTest('API Manager', 'Handle Error Response', async () => {
      const response = await this.services.api.makeRequest(
        'test-api',
        testConfig,
        {
          method: 'GET',
          endpoint: '/status/404'
        }
      );
      
      if (response.success) {
        throw new Error('Expected error response but got success');
      }
      
      if (response.statusCode !== 404) {
        throw new Error(`Expected 404 status but got ${response.statusCode}`);
      }
    });
  }

  private async testIntegration(): Promise<void> {
    await this.runTest('Integration', 'Service Communication', async () => {
      // Test that services can communicate through realtime updates
      let settingsEventReceived = false;
      
      this.services.realtime.subscribe(
        'integration-test',
        ['settings_changed'],
        () => { settingsEventReceived = true; }
      );
      
      // Trigger a settings change
      await this.services.settings.updateSetting('creativityLevel', 80);
      
      // Wait for event
      await new Promise(resolve => setTimeout(resolve, 200));
      
      this.services.realtime.unsubscribe('integration-test');
      
      if (!settingsEventReceived) {
        throw new Error('Services not communicating through realtime updates');
      }
    });

    await this.runTest('Integration', 'End-to-End Recipe Generation Flow', async () => {
      // This would test the complete flow from settings to recipe generation
      // For now, just verify that all required services are available
      const llmConfig = this.services.llm.getConfiguration();
      const settings = this.services.settings.getSettings();
      
      if (!llmConfig || !settings) {
        throw new Error('Required services not properly configured for recipe generation');
      }
    });
  }

  private printTestSummary(): void {
    console.log('\n📊 Test Summary:');
    console.log('================');
    
    let totalPassed = 0;
    let totalFailed = 0;
    let totalDuration = 0;
    
    for (const suite of this.testResults) {
      console.log(`\n${suite.name}:`);
      console.log(`  ✅ Passed: ${suite.totalPassed}`);
      console.log(`  ❌ Failed: ${suite.totalFailed}`);
      console.log(`  ⏱️  Duration: ${suite.totalDuration}ms`);
      
      totalPassed += suite.totalPassed;
      totalFailed += suite.totalFailed;
      totalDuration += suite.totalDuration;
      
      if (suite.totalFailed > 0) {
        console.log('  Failed tests:');
        suite.tests
          .filter(t => !t.passed)
          .forEach(t => console.log(`    - ${t.testName}: ${t.error}`));
      }
    }
    
    console.log('\n🎯 Overall Results:');
    console.log(`  Total Tests: ${totalPassed + totalFailed}`);
    console.log(`  Passed: ${totalPassed}`);
    console.log(`  Failed: ${totalFailed}`);
    console.log(`  Success Rate: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
    console.log(`  Total Duration: ${totalDuration}ms`);
    
    if (totalFailed === 0) {
      console.log('\n🎉 All tests passed! ChefAI is ready to go!');
    } else {
      console.log(`\n⚠️  ${totalFailed} test(s) failed. Please review and fix issues.`);
    }
  }

  public getTestResults(): TestSuite[] {
    return this.testResults;
  }
}

// Export for use in development/testing
export default ChefAIIntegrationTest;
