import React from 'react';
import { Recipe } from '../types';
import { useI18n } from '../hooks/useI18n';
import { formatDistanceToNow } from 'date-fns';

interface RecentRecipesProps {
  recipes: Recipe[];
  onRecipeClick: (recipe: Recipe) => void;
  compact?: boolean;
}

export const RecentRecipes: React.FC<RecentRecipesProps> = ({
  recipes,
  onRecipeClick,
  compact = false
}) => {
  const { t } = useI18n();

  if (recipes.length === 0) {
    return (
      <div className="glass-card-sm" style={{ textAlign: 'center', opacity: 0.7 }}>
        <p style={{ margin: 0 }}>{t('messages.no_recent_recipes')}</p>
      </div>
    );
  }

  return (
    <div className="recipe-list">
      {recipes.map((recipe) => (
        <div
          key={recipe.id}
          className="recipe-item"
          onClick={() => onRecipeClick(recipe)}
          role="button"
          tabIndex={0}
          onKeyPress={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              onRecipeClick(recipe);
            }
          }}
        >
          <div className="recipe-item-header">
            <h4 className="recipe-item-title">{recipe.title}</h4>
            <span className="recipe-item-time">
              {formatDistanceToNow(new Date(recipe.createdAt), { addSuffix: true })}
            </span>
          </div>
          
          {!compact && recipe.description && (
            <p style={{ 
              margin: '0 0 var(--spacing-xs)', 
              fontSize: 'var(--font-size-xs)', 
              opacity: 0.8,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {recipe.description}
            </p>
          )}
          
          <div className="recipe-item-meta">
            <div className="recipe-item-tags">
              {recipe.cuisine && (
                <span className="recipe-item-tag">{recipe.cuisine}</span>
              )}
              <span className="recipe-item-tag">{recipe.difficulty}</span>
              {recipe.tags.slice(0, compact ? 1 : 2).map((tag, index) => (
                <span key={index} className="recipe-item-tag">{tag}</span>
              ))}
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--spacing-xs)' }}>
              <span>⏱️ {recipe.prepTime + recipe.cookTime}min</span>
              <span>👥 {recipe.servings}</span>
              {recipe.rating > 0 && (
                <span>⭐ {recipe.rating.toFixed(1)}</span>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
