# 🧪 ChefAI API Testing Guide

## 🚀 Quick Test Steps

### 1. Get Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/)
2. Sign in with your Google account
3. Click "Get API Key" 
4. Create a new API key
5. Copy the API key

### 2. Test the Extension

#### Method A: Using Test Page
1. Open `test-api.html` in Chrome
2. Enter your Gemini API key
3. Click "Test Connection"
4. If successful, try "Generate Recipe"

#### Method B: Using Extension Interface
1. Load the extension in Chrome (`chrome://extensions/`)
2. Click the ChefAI icon 🍳
3. Go to Settings → Models tab
4. Enter your API key in the Gemini provider
5. Click "Test Connection"
6. If successful, try the sidebar interface

### 3. Test Recipe Generation

#### Using Sidebar Interface:
1. Visit any website
2. Press `Ctrl+Shift+C` or click the floating 🍳 button
3. Enter ingredients: "chicken, rice, vegetables, soy sauce"
4. Press Enter or click Send
5. Wait for AI-generated recipe

#### Expected Result:
```
🍳 Chicken Fried Rice

📝 Description:
A delicious and easy-to-make chicken fried rice with vegetables...

📋 Ingredients:
• 2 cups cooked rice
• 1 lb chicken breast, diced
• 1 cup mixed vegetables
• 2 tbsp soy sauce
• ...

👨‍🍳 Instructions:
1. Heat oil in a large pan or wok over medium-high heat
2. Add diced chicken and cook until golden brown
3. Add vegetables and stir-fry for 3-4 minutes
...

ℹ️ Cooking Information:
prep_time: 15 minutes
cook_time: 20 minutes
total_time: 35 minutes
servings: 4 people
```

## 🔧 Troubleshooting

### Common Issues:

**❌ "API key not configured"**
- Make sure you entered the API key correctly
- Check if the key has proper permissions
- Try regenerating the API key

**❌ "Failed to generate recipe"**
- Check your internet connection
- Verify API key is valid and has credits
- Try with simpler ingredients

**❌ "Extension not loading"**
- Refresh the webpage
- Check Chrome console for errors (F12)
- Reload the extension in chrome://extensions/

**❌ "Sidebar not appearing"**
- Try pressing Ctrl+Shift+C
- Check if other extensions are conflicting
- Try on a different website

## 🎯 Testing Checklist

### ✅ Basic Functionality
- [ ] Extension loads without errors
- [ ] Settings page opens correctly
- [ ] API key can be saved
- [ ] Connection test passes
- [ ] Floating button appears on websites
- [ ] Sidebar opens with Ctrl+Shift+C
- [ ] Recipe generation works

### ✅ Advanced Features
- [ ] Multiple model sections (Planner, Navigator, Validator)
- [ ] Temperature and Top-P sliders work
- [ ] Provider dropdown shows all options
- [ ] Firewall settings can be configured
- [ ] General settings save properly

### ✅ UI/UX Testing
- [ ] Dark theme displays correctly
- [ ] Glassmorphism effects work
- [ ] Animations are smooth
- [ ] Text is readable
- [ ] Buttons respond to hover
- [ ] Mobile responsiveness (if applicable)

## 📊 Performance Testing

### Recipe Generation Speed:
- **Expected**: 3-8 seconds for typical recipe
- **Acceptable**: Up to 15 seconds for complex recipes
- **Timeout**: 30 seconds maximum

### Memory Usage:
- **Extension**: < 50MB
- **API Calls**: Minimal impact
- **Storage**: < 1MB for settings

## 🔍 Debug Information

### Chrome Console Commands:
```javascript
// Check extension status
chrome.storage.local.get(null, console.log);

// Test API directly
const api = new GeminiAPI();
api.initialize().then(console.log);

// Check sidebar status
window.ChefAIInjector?.getStatus();
```

### Log Locations:
- **Extension Console**: Right-click extension → Inspect popup
- **Content Script**: F12 → Console on any webpage
- **Background Script**: chrome://extensions/ → Background page

## 🎉 Success Criteria

The extension is working correctly if:

1. ✅ **API Integration**: Gemini API responds with valid recipes
2. ✅ **UI Functionality**: All buttons, sliders, and inputs work
3. ✅ **Recipe Quality**: Generated recipes are coherent and useful
4. ✅ **Performance**: Response time under 10 seconds
5. ✅ **Error Handling**: Graceful error messages for failures
6. ✅ **Settings Persistence**: Configuration saves between sessions

## 📞 Support

If you encounter issues:

1. **Check Console**: Look for error messages in browser console
2. **Verify API Key**: Ensure Gemini API key is valid and has credits
3. **Test Connection**: Use the built-in connection test
4. **Clear Storage**: Reset extension settings if needed
5. **Reload Extension**: Disable and re-enable in chrome://extensions/

---

**🍳 Happy Testing with ChefAI!**

*Generate amazing recipes with the power of AI*
