// Gemini API Integration for ChefAI
class GeminiAPI {
  constructor() {
    this.baseURL = 'https://generativelanguage.googleapis.com/v1beta/models';
    this.apiKey = null;
    this.defaultModel = 'gemini-2.5-flash-preview-05-20';
  }

  async initialize() {
    try {
      // Try multiple storage keys for compatibility
      const result = await chrome.storage.local.get([
        'chefai_llm_config',
        'chefai_providers',
        'gemini_api_key'
      ]);

      // Check different possible locations for the API key
      if (result.chefai_llm_config && result.chefai_llm_config.apiKey) {
        this.apiKey = result.chefai_llm_config.apiKey;
        return true;
      }

      // Check if stored in providers
      if (result.chefai_providers) {
        const geminiProvider = result.chefai_providers.find(p => p.name === 'Gemini' || p.type === 'gemini');
        if (geminiProvider && geminiProvider.apiKey) {
          this.apiKey = geminiProvider.apiKey;
          return true;
        }
      }

      // Check direct storage
      if (result.gemini_api_key) {
        this.apiKey = result.gemini_api_key;
        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to initialize Gemini API:', error);
      return false;
    }
  }

  async generateRecipe(ingredients, preferences = {}) {
    if (!this.apiKey) {
      throw new Error('API key not configured. Please set up your Gemini API key in settings.');
    }

    const prompt = this.buildRecipePrompt(ingredients, preferences);
    
    try {
      const response = await this.makeRequest(prompt);
      return this.parseRecipeResponse(response);
    } catch (error) {
      console.error('Recipe generation failed:', error);
      throw new Error(`Failed to generate recipe: ${error.message}`);
    }
  }

  buildRecipePrompt(ingredients, preferences) {
    let prompt = `You are ChefAI, an expert culinary assistant. Generate a detailed, delicious recipe using the following ingredients: ${ingredients.join(', ')}.

Requirements:
- Create a complete recipe with title, description, ingredients list, and step-by-step instructions
- Use primarily the provided ingredients, but you may suggest common pantry items if needed
- Make the recipe practical and achievable for home cooking
- Include cooking time, prep time, and serving size
- Format the response in clean, readable sections

`;

    if (preferences.cuisine) {
      prompt += `- Style: ${preferences.cuisine} cuisine\n`;
    }
    
    if (preferences.dietary) {
      prompt += `- Dietary requirements: ${preferences.dietary}\n`;
    }
    
    if (preferences.difficulty) {
      prompt += `- Difficulty level: ${preferences.difficulty}\n`;
    }
    
    if (preferences.cookingTime) {
      prompt += `- Maximum cooking time: ${preferences.cookingTime} minutes\n`;
    }

    prompt += `
Please format your response as follows:

# Recipe Title

## Description
Brief description of the dish

## Ingredients
- List all ingredients with measurements

## Instructions
1. Step-by-step cooking instructions
2. Include cooking times and temperatures
3. Provide helpful tips

## Cooking Information
- Prep Time: X minutes
- Cook Time: X minutes  
- Total Time: X minutes
- Servings: X people
- Difficulty: Easy/Medium/Hard

Generate an amazing recipe now!`;

    return prompt;
  }

  async makeRequest(prompt, model = null) {
    const modelToUse = model || this.defaultModel;
    const url = `${this.baseURL}/${modelToUse}:generateContent?key=${this.apiKey}`;
    
    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topP: 0.9,
        maxOutputTokens: 2048,
      }
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`API request failed: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Invalid response format from Gemini API');
    }

    return data.candidates[0].content.parts[0].text;
  }

  parseRecipeResponse(response) {
    // Basic parsing - in a real implementation, you might want more sophisticated parsing
    const lines = response.split('\n');
    let title = 'Generated Recipe';
    let description = '';
    let ingredients = [];
    let instructions = [];
    let cookingInfo = {};
    
    let currentSection = '';
    
    for (let line of lines) {
      line = line.trim();
      
      if (line.startsWith('# ')) {
        title = line.substring(2);
      } else if (line.startsWith('## Description')) {
        currentSection = 'description';
      } else if (line.startsWith('## Ingredients')) {
        currentSection = 'ingredients';
      } else if (line.startsWith('## Instructions')) {
        currentSection = 'instructions';
      } else if (line.startsWith('## Cooking Information')) {
        currentSection = 'cooking-info';
      } else if (line && !line.startsWith('#')) {
        switch (currentSection) {
          case 'description':
            if (line) description += line + ' ';
            break;
          case 'ingredients':
            if (line.startsWith('- ')) {
              ingredients.push(line.substring(2));
            }
            break;
          case 'instructions':
            if (line.match(/^\d+\./)) {
              instructions.push(line);
            }
            break;
          case 'cooking-info':
            if (line.includes(':')) {
              const [key, value] = line.split(':').map(s => s.trim());
              cookingInfo[key.toLowerCase().replace(' ', '_')] = value;
            }
            break;
        }
      }
    }

    return {
      title: title.trim(),
      description: description.trim(),
      ingredients,
      instructions,
      cookingInfo,
      rawResponse: response
    };
  }

  async testConnection() {
    if (!this.apiKey) {
      return { success: false, error: 'API key not configured' };
    }

    try {
      const testPrompt = 'Say "Hello from ChefAI!" to test the connection.';
      const response = await this.makeRequest(testPrompt);
      
      if (response && response.includes('Hello from ChefAI!')) {
        return { success: true, message: 'Connection successful!' };
      } else {
        return { success: true, message: 'Connection working, but unexpected response format.' };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async getAvailableModels() {
    if (!this.apiKey) {
      return [];
    }

    try {
      const url = `https://generativelanguage.googleapis.com/v1beta/models?key=${this.apiKey}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status}`);
      }
      
      const data = await response.json();
      return data.models?.map(model => ({
        name: model.name.replace('models/', ''),
        displayName: model.displayName || model.name,
        description: model.description || ''
      })) || [];
    } catch (error) {
      console.error('Failed to fetch available models:', error);
      return [
        { name: 'gemini-2.5-flash-preview-05-20', displayName: 'Gemini 2.5 Flash Preview' },
        { name: 'gemini-2.5-pro-preview-06-05', displayName: 'Gemini 2.5 Pro Preview' },
        { name: 'gemini-2.0-flash', displayName: 'Gemini 2.0 Flash' }
      ];
    }
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GeminiAPI;
} else {
  window.GeminiAPI = GeminiAPI;
}
