// API Manager - Unified system for handling multiple APIs with better error handling
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
  retryAfter?: number;
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
    cost?: number;
  };
}

export interface APIConfig {
  baseURL: string;
  apiKey: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  rateLimit?: {
    requestsPerMinute: number;
    requestsPerHour: number;
  };
}

export interface RequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  endpoint: string;
  data?: any;
  headers?: Record<string, string>;
  timeout?: number;
  retryAttempts?: number;
}

export class APIManager {
  private static instance: APIManager;
  private requestQueue: Map<string, number> = new Map();
  private rateLimiters: Map<string, { requests: number; resetTime: number }> = new Map();

  private constructor() {}

  public static getInstance(): APIManager {
    if (!APIManager.instance) {
      APIManager.instance = new APIManager();
    }
    return APIManager.instance;
  }

  public async makeRequest<T>(
    apiId: string,
    config: APIConfig,
    options: RequestOptions
  ): Promise<APIResponse<T>> {
    try {
      // Check rate limits
      if (!this.checkRateLimit(apiId, config.rateLimit)) {
        return {
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
          statusCode: 429
        };
      }

      // Execute request with retry logic
      return await this.executeWithRetry(config, options);
    } catch (error) {
      console.error(`API request failed for ${apiId}:`, error);
      return {
        success: false,
        error: error.message || 'Unknown API error',
        statusCode: 500
      };
    }
  }

  private async executeWithRetry<T>(
    config: APIConfig,
    options: RequestOptions
  ): Promise<APIResponse<T>> {
    const maxAttempts = options.retryAttempts || config.retryAttempts || 3;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await this.executeRequest<T>(config, options);
        
        // If successful, return immediately
        if (response.success) {
          return response;
        }

        // If it's a rate limit error, wait and retry
        if (response.statusCode === 429) {
          const retryAfter = response.retryAfter || config.retryDelay || 1000;
          await this.delay(retryAfter * attempt);
          continue;
        }

        // If it's a server error (5xx), retry
        if (response.statusCode && response.statusCode >= 500) {
          await this.delay(config.retryDelay * attempt);
          continue;
        }

        // For client errors (4xx), don't retry
        return response;
      } catch (error) {
        lastError = error;
        if (attempt < maxAttempts) {
          await this.delay(config.retryDelay * attempt);
        }
      }
    }

    return {
      success: false,
      error: lastError?.message || 'Request failed after all retry attempts',
      statusCode: 500
    };
  }

  private async executeRequest<T>(
    config: APIConfig,
    options: RequestOptions
  ): Promise<APIResponse<T>> {
    const controller = new AbortController();
    const timeout = options.timeout || config.timeout || 30000;

    // Set up timeout
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const url = `${config.baseURL}${options.endpoint}`;
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        ...options.headers
      };

      const fetchOptions: RequestInit = {
        method: options.method,
        headers,
        signal: controller.signal
      };

      if (options.data && (options.method === 'POST' || options.method === 'PUT')) {
        fetchOptions.body = JSON.stringify(options.data);
      }

      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);

      // Handle different response types
      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      if (!response.ok) {
        return {
          success: false,
          error: this.extractErrorMessage(responseData, response.status),
          statusCode: response.status,
          retryAfter: this.getRetryAfter(response)
        };
      }

      return {
        success: true,
        data: responseData,
        statusCode: response.status,
        usage: this.extractUsageInfo(responseData)
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        return {
          success: false,
          error: 'Request timeout',
          statusCode: 408
        };
      }

      throw error;
    }
  }

  private checkRateLimit(apiId: string, rateLimit?: APIConfig['rateLimit']): boolean {
    if (!rateLimit) return true;

    const now = Date.now();
    const limiter = this.rateLimiters.get(apiId);

    if (!limiter || now > limiter.resetTime) {
      // Reset rate limiter
      this.rateLimiters.set(apiId, {
        requests: 1,
        resetTime: now + 60000 // 1 minute
      });
      return true;
    }

    if (limiter.requests >= rateLimit.requestsPerMinute) {
      return false;
    }

    limiter.requests++;
    return true;
  }

  private extractErrorMessage(responseData: any, statusCode: number): string {
    // Try to extract error message from different API response formats
    if (typeof responseData === 'string') {
      return responseData;
    }

    if (responseData?.error) {
      if (typeof responseData.error === 'string') {
        return responseData.error;
      }
      if (responseData.error.message) {
        return responseData.error.message;
      }
    }

    if (responseData?.message) {
      return responseData.message;
    }

    if (responseData?.detail) {
      return responseData.detail;
    }

    // Default error messages based on status code
    switch (statusCode) {
      case 400:
        return 'Bad request - please check your input';
      case 401:
        return 'Unauthorized - please check your API key';
      case 403:
        return 'Forbidden - insufficient permissions';
      case 404:
        return 'Not found - the requested resource does not exist';
      case 429:
        return 'Rate limit exceeded - please try again later';
      case 500:
        return 'Internal server error - please try again later';
      case 502:
        return 'Bad gateway - service temporarily unavailable';
      case 503:
        return 'Service unavailable - please try again later';
      default:
        return `Request failed with status ${statusCode}`;
    }
  }

  private getRetryAfter(response: Response): number | undefined {
    const retryAfter = response.headers.get('retry-after');
    if (retryAfter) {
      const seconds = parseInt(retryAfter, 10);
      return isNaN(seconds) ? undefined : seconds * 1000; // Convert to milliseconds
    }
    return undefined;
  }

  private extractUsageInfo(responseData: any): APIResponse['usage'] | undefined {
    // Extract usage information from different API response formats
    if (responseData?.usage) {
      return {
        promptTokens: responseData.usage.prompt_tokens || responseData.usage.input_tokens,
        completionTokens: responseData.usage.completion_tokens || responseData.usage.output_tokens,
        totalTokens: responseData.usage.total_tokens,
        cost: responseData.usage.cost
      };
    }

    if (responseData?.usageMetadata) {
      return {
        promptTokens: responseData.usageMetadata.promptTokenCount,
        completionTokens: responseData.usageMetadata.candidatesTokenCount,
        totalTokens: responseData.usageMetadata.totalTokenCount
      };
    }

    return undefined;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public async testConnection(apiId: string, config: APIConfig): Promise<APIResponse> {
    const testOptions: RequestOptions = {
      method: 'GET',
      endpoint: '/models', // Common endpoint for testing
      timeout: 10000,
      retryAttempts: 1
    };

    try {
      const response = await this.makeRequest(apiId, config, testOptions);
      
      if (response.success) {
        return {
          success: true,
          data: 'Connection successful'
        };
      }

      return response;
    } catch (error) {
      return {
        success: false,
        error: `Connection test failed: ${error.message}`
      };
    }
  }

  public getQueueStatus(apiId: string): number {
    return this.requestQueue.get(apiId) || 0;
  }

  public getRateLimitStatus(apiId: string): { requests: number; resetTime: number } | null {
    return this.rateLimiters.get(apiId) || null;
  }

  public clearRateLimit(apiId: string): void {
    this.rateLimiters.delete(apiId);
  }

  public async healthCheck(apis: Array<{ id: string; config: APIConfig }>): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    const healthChecks = apis.map(async ({ id, config }) => {
      try {
        const response = await this.testConnection(id, config);
        results[id] = response.success;
      } catch (error) {
        results[id] = false;
      }
    });

    await Promise.allSettled(healthChecks);
    return results;
  }
}
