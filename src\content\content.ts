// Content script for ChefAI Chrome Extension
// Runs on all web pages to detect and extract recipe content

interface RecipeData {
  title?: string;
  ingredients?: string[];
  instructions?: string[];
  prepTime?: string;
  cookTime?: string;
  servings?: string;
  image?: string;
  rating?: number;
  reviews?: number;
}

class ContentScriptService {
  private static instance: ContentScriptService;
  private isInitialized = false;
  private recipeData: RecipeData | null = null;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): ContentScriptService {
    if (!ContentScriptService.instance) {
      ContentScriptService.instance = new ContentScriptService();
    }
    return ContentScriptService.instance;
  }

  private initialize(): void {
    if (this.isInitialized) return;

    try {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          this.setupContentScript();
        });
      } else {
        this.setupContentScript();
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize content script:', error);
    }
  }

  private setupContentScript(): void {
    // Set up message listener
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });

    // Detect if current page contains recipe content
    this.detectRecipeContent();

    // Set up mutation observer to detect dynamic content
    this.setupMutationObserver();

    // Add floating action button for recipe extraction
    this.addFloatingActionButton();
  }

  private handleMessage(
    message: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): void {
    switch (message.type) {
      case 'EXTRACT_RECIPE':
        const recipe = this.extractRecipeFromPage();
        sendResponse({ success: true, data: recipe });
        break;

      case 'GET_PAGE_INGREDIENTS':
        const ingredients = this.extractIngredientsFromPage();
        sendResponse({ success: true, data: ingredients });
        break;

      case 'HIGHLIGHT_INGREDIENTS':
        this.highlightIngredients(message.data.ingredients);
        sendResponse({ success: true });
        break;

      default:
        sendResponse({ success: false, error: 'Unknown message type' });
    }
  }

  private detectRecipeContent(): void {
    // Check for structured data (JSON-LD)
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    
    for (const script of jsonLdScripts) {
      try {
        const data = JSON.parse(script.textContent || '');
        if (this.isRecipeStructuredData(data)) {
          this.recipeData = this.parseStructuredRecipeData(data);
          this.notifyRecipeDetected();
          return;
        }
      } catch (error) {
        // Ignore parsing errors
      }
    }

    // Check for microdata
    const microdataRecipe = this.extractMicrodataRecipe();
    if (microdataRecipe) {
      this.recipeData = microdataRecipe;
      this.notifyRecipeDetected();
      return;
    }

    // Check for common recipe website patterns
    const heuristicRecipe = this.extractRecipeHeuristically();
    if (heuristicRecipe) {
      this.recipeData = heuristicRecipe;
      this.notifyRecipeDetected();
    }
  }

  private isRecipeStructuredData(data: any): boolean {
    if (Array.isArray(data)) {
      return data.some(item => this.isRecipeStructuredData(item));
    }
    
    return data['@type'] === 'Recipe' || 
           (data['@graph'] && data['@graph'].some((item: any) => item['@type'] === 'Recipe'));
  }

  private parseStructuredRecipeData(data: any): RecipeData {
    let recipe = data;
    
    if (Array.isArray(data)) {
      recipe = data.find(item => item['@type'] === 'Recipe');
    } else if (data['@graph']) {
      recipe = data['@graph'].find((item: any) => item['@type'] === 'Recipe');
    }

    return {
      title: recipe.name,
      ingredients: this.parseIngredients(recipe.recipeIngredient),
      instructions: this.parseInstructions(recipe.recipeInstructions),
      prepTime: recipe.prepTime,
      cookTime: recipe.cookTime,
      servings: recipe.recipeYield,
      image: recipe.image?.url || recipe.image,
      rating: recipe.aggregateRating?.ratingValue,
      reviews: recipe.aggregateRating?.reviewCount
    };
  }

  private parseIngredients(ingredients: any): string[] {
    if (!ingredients) return [];
    
    if (Array.isArray(ingredients)) {
      return ingredients.map(ingredient => 
        typeof ingredient === 'string' ? ingredient : ingredient.text || ingredient.name || ''
      ).filter(Boolean);
    }
    
    return [ingredients];
  }

  private parseInstructions(instructions: any): string[] {
    if (!instructions) return [];
    
    if (Array.isArray(instructions)) {
      return instructions.map(instruction => {
        if (typeof instruction === 'string') return instruction;
        return instruction.text || instruction.name || '';
      }).filter(Boolean);
    }
    
    return [instructions];
  }

  private extractMicrodataRecipe(): RecipeData | null {
    const recipeElement = document.querySelector('[itemtype*="Recipe"]');
    if (!recipeElement) return null;

    return {
      title: this.getMicrodataProperty(recipeElement, 'name'),
      ingredients: this.getMicrodataProperties(recipeElement, 'recipeIngredient'),
      instructions: this.getMicrodataProperties(recipeElement, 'recipeInstructions'),
      prepTime: this.getMicrodataProperty(recipeElement, 'prepTime'),
      cookTime: this.getMicrodataProperty(recipeElement, 'cookTime'),
      servings: this.getMicrodataProperty(recipeElement, 'recipeYield'),
      image: this.getMicrodataProperty(recipeElement, 'image')
    };
  }

  private getMicrodataProperty(element: Element, property: string): string | undefined {
    const propElement = element.querySelector(`[itemprop="${property}"]`);
    return propElement?.textContent?.trim() || undefined;
  }

  private getMicrodataProperties(element: Element, property: string): string[] {
    const propElements = element.querySelectorAll(`[itemprop="${property}"]`);
    return Array.from(propElements).map(el => el.textContent?.trim() || '').filter(Boolean);
  }

  private extractRecipeHeuristically(): RecipeData | null {
    // Common selectors for recipe websites
    const titleSelectors = [
      'h1.recipe-title',
      'h1.entry-title',
      '.recipe-header h1',
      '.recipe-title',
      'h1[class*="recipe"]',
      'h1[class*="title"]'
    ];

    const ingredientSelectors = [
      '.recipe-ingredients li',
      '.ingredients li',
      '[class*="ingredient"] li',
      '.recipe-ingredient',
      '.ingredient-list li'
    ];

    const instructionSelectors = [
      '.recipe-instructions li',
      '.instructions li',
      '.recipe-method li',
      '.directions li',
      '[class*="instruction"] li'
    ];

    const title = this.getTextFromSelectors(titleSelectors);
    const ingredients = this.getTextsFromSelectors(ingredientSelectors);
    const instructions = this.getTextsFromSelectors(instructionSelectors);

    if (title || ingredients.length > 0 || instructions.length > 0) {
      return {
        title,
        ingredients: ingredients.length > 0 ? ingredients : undefined,
        instructions: instructions.length > 0 ? instructions : undefined
      };
    }

    return null;
  }

  private getTextFromSelectors(selectors: string[]): string | undefined {
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        return element.textContent.trim();
      }
    }
    return undefined;
  }

  private getTextsFromSelectors(selectors: string[]): string[] {
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        return Array.from(elements)
          .map(el => el.textContent?.trim() || '')
          .filter(Boolean);
      }
    }
    return [];
  }

  private setupMutationObserver(): void {
    const observer = new MutationObserver((mutations) => {
      let shouldRecheck = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldRecheck = true;
        }
      });

      if (shouldRecheck && !this.recipeData) {
        setTimeout(() => this.detectRecipeContent(), 1000);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private addFloatingActionButton(): void {
    if (!this.recipeData) return;

    const fab = document.createElement('div');
    fab.id = 'chefai-fab';
    fab.innerHTML = '🍳';
    fab.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      z-index: 10000;
      transition: transform 0.2s ease;
    `;

    fab.addEventListener('mouseenter', () => {
      fab.style.transform = 'scale(1.1)';
    });

    fab.addEventListener('mouseleave', () => {
      fab.style.transform = 'scale(1)';
    });

    fab.addEventListener('click', () => {
      this.openRecipeExtractor();
    });

    document.body.appendChild(fab);
  }

  private notifyRecipeDetected(): void {
    // Notify background script that recipe was detected
    chrome.runtime.sendMessage({
      type: 'RECIPE_DETECTED',
      data: this.recipeData
    });
  }

  private extractRecipeFromPage(): RecipeData | null {
    return this.recipeData;
  }

  private extractIngredientsFromPage(): string[] {
    const selectedText = window.getSelection()?.toString().trim();
    if (selectedText) {
      // Parse selected text for ingredients
      return this.parseTextForIngredients(selectedText);
    }

    // Return detected recipe ingredients
    return this.recipeData?.ingredients || [];
  }

  private parseTextForIngredients(text: string): string[] {
    // Simple ingredient parsing - split by lines and filter
    return text.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && line.length < 100)
      .slice(0, 20); // Limit to 20 ingredients
  }

  private highlightIngredients(ingredients: string[]): void {
    // Highlight ingredients on the page
    ingredients.forEach(ingredient => {
      this.highlightTextOnPage(ingredient);
    });
  }

  private highlightTextOnPage(text: string): void {
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      null
    );

    const textNodes: Text[] = [];
    let node;
    
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }

    textNodes.forEach(textNode => {
      const content = textNode.textContent || '';
      if (content.toLowerCase().includes(text.toLowerCase())) {
        const parent = textNode.parentNode;
        if (parent) {
          const highlightedContent = content.replace(
            new RegExp(text, 'gi'),
            `<mark style="background-color: #fbbf24; padding: 2px 4px; border-radius: 3px;">$&</mark>`
          );
          
          const wrapper = document.createElement('span');
          wrapper.innerHTML = highlightedContent;
          parent.replaceChild(wrapper, textNode);
        }
      }
    });
  }

  private openRecipeExtractor(): void {
    // Open ChefAI with extracted recipe data
    chrome.runtime.sendMessage({
      type: 'OPEN_RECIPE_EXTRACTOR',
      data: this.recipeData
    });
  }
}

// Initialize content script
ContentScriptService.getInstance();
