import React, { useState, useEffect } from 'react';
import { useTheme } from '../hooks/useTheme';
import { useI18n } from '../hooks/useI18n';
import { ThemeSettings } from '../types';
import { LLMService, LLMConfig, LLMProvider } from '../services/LLMService';

interface SettingsPanelProps {
  onClose?: () => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const { theme, updateTheme, resetTheme, toggleArtMode, getPresetThemes, exportTheme, importTheme } = useTheme();
  const { t, currentLanguage, changeLanguage, getAvailableLanguages } = useI18n();
  const [activeTab, setActiveTab] = useState<'appearance' | 'language' | 'ai' | 'privacy' | 'advanced'>('appearance');
  const [apiKey, setApiKey] = useState('');

  // LLM Configuration State
  const [llmConfig, setLlmConfig] = useState<LLMConfig | null>(null);
  const [llmProviders, setLlmProviders] = useState<LLMProvider[]>([]);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<string>('');

  // Advanced Settings State
  const [targetAudience, setTargetAudience] = useState<string>('family');
  const [recipeComplexity, setRecipeComplexity] = useState<string>('medium');
  const [culturalPreferences, setCulturalPreferences] = useState<string[]>([]);
  const [dietaryProfile, setDietaryProfile] = useState<string[]>([]);
  const [cookingSkillLevel, setCookingSkillLevel] = useState<string>('intermediate');
  const [preferredCookingMethods, setPreferredCookingMethods] = useState<string[]>([]);
  const [kitchenEquipment, setKitchenEquipment] = useState<string[]>([]);
  const [timeConstraints, setTimeConstraints] = useState<string>('flexible');
  const [budgetRange, setBudgetRange] = useState<string>('medium');
  const [seasonalPreferences, setSeasonalPreferences] = useState<boolean>(true);
  const [healthGoals, setHealthGoals] = useState<string[]>([]);

  const llmService = LLMService.getInstance();

  const availableLanguages = getAvailableLanguages();
  const presetThemes = getPresetThemes();

  // Load LLM configuration and settings on component mount
  useEffect(() => {
    loadLLMConfiguration();
    loadAdvancedSettings();
  }, []);

  const loadLLMConfiguration = async () => {
    const config = llmService.getConfiguration();
    const providers = llmService.getProviders();
    setLlmConfig(config);
    setLlmProviders(providers);
    if (config?.apiKey) {
      setApiKey(config.apiKey);
    }
  };

  const loadAdvancedSettings = async () => {
    try {
      const result = await chrome.storage.local.get([
        'targetAudience', 'recipeComplexity', 'culturalPreferences', 'dietaryProfile',
        'cookingSkillLevel', 'preferredCookingMethods', 'kitchenEquipment', 'timeConstraints',
        'budgetRange', 'seasonalPreferences', 'healthGoals'
      ]);

      if (result.targetAudience) setTargetAudience(result.targetAudience);
      if (result.recipeComplexity) setRecipeComplexity(result.recipeComplexity);
      if (result.culturalPreferences) setCulturalPreferences(result.culturalPreferences);
      if (result.dietaryProfile) setDietaryProfile(result.dietaryProfile);
      if (result.cookingSkillLevel) setCookingSkillLevel(result.cookingSkillLevel);
      if (result.preferredCookingMethods) setPreferredCookingMethods(result.preferredCookingMethods);
      if (result.kitchenEquipment) setKitchenEquipment(result.kitchenEquipment);
      if (result.timeConstraints) setTimeConstraints(result.timeConstraints);
      if (result.budgetRange) setBudgetRange(result.budgetRange);
      if (result.seasonalPreferences !== undefined) setSeasonalPreferences(result.seasonalPreferences);
      if (result.healthGoals) setHealthGoals(result.healthGoals);
    } catch (error) {
      console.error('Failed to load advanced settings:', error);
    }
  };

  const saveAdvancedSettings = async () => {
    try {
      await chrome.storage.local.set({
        targetAudience,
        recipeComplexity,
        culturalPreferences,
        dietaryProfile,
        cookingSkillLevel,
        preferredCookingMethods,
        kitchenEquipment,
        timeConstraints,
        budgetRange,
        seasonalPreferences,
        healthGoals
      });
      alert('Advanced settings saved successfully!');
    } catch (error) {
      console.error('Failed to save advanced settings:', error);
      alert('Failed to save advanced settings.');
    }
  };

  const handleColorChange = (type: 'primary' | 'accent', color: string) => {
    updateTheme({
      [type === 'primary' ? 'primaryColor' : 'accentColor']: color
    });
  };

  const handleLLMConfigSave = async () => {
    if (!llmConfig) return;

    try {
      await llmService.saveConfiguration(llmConfig);
      alert('LLM configuration saved successfully!');
    } catch (error) {
      console.error('Failed to save LLM configuration:', error);
      alert('Failed to save LLM configuration.');
    }
  };

  const handleTestConnection = async () => {
    if (!llmConfig?.apiKey) {
      setTestResult('❌ Please enter an API key first');
      return;
    }

    setIsTestingConnection(true);
    setTestResult('');

    try {
      const result = await llmService.testConnection();
      setTestResult(result.success ? '✅ Connection successful!' : `❌ ${result.error}`);
    } catch (error) {
      setTestResult(`❌ Connection failed: ${error.message}`);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleArrayToggle = (array: string[], setArray: (arr: string[]) => void, value: string) => {
    if (array.includes(value)) {
      setArray(array.filter(item => item !== value));
    } else {
      setArray([...array, value]);
    }
  };

  const handleExportTheme = () => {
    const themeData = exportTheme();
    const blob = new Blob([themeData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'chefai-theme.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImportTheme = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const content = e.target?.result as string;
        const success = await importTheme(content);
        if (success) {
          alert(t('settings.theme_imported_successfully'));
        } else {
          alert(t('settings.theme_import_failed'));
        }
      };
      reader.readAsText(file);
    }
  };

  const tabs = [
    { id: 'appearance', name: t('settings.appearance'), icon: '🎨' },
    { id: 'language', name: t('settings.language'), icon: '🌍' },
    { id: 'ai', name: 'LLM Configuration', icon: '🤖' },
    { id: 'advanced', name: 'Advanced Settings', icon: '⚙️' },
    { id: 'privacy', name: t('settings.privacy'), icon: '🔒' }
  ];

  return (
    <div className="settings-panel glass-container">
      <div className="settings-header">
        <h2 className="settings-title">{t('settings.title')}</h2>
        {onClose && (
          <button className="settings-close" onClick={onClose}>×</button>
        )}
      </div>

      <div className="settings-tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`settings-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id as any)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-name">{tab.name}</span>
          </button>
        ))}
      </div>

      <div className="settings-content">
        {activeTab === 'appearance' && (
          <div className="settings-section">
            <h3>{t('settings.appearance')}</h3>
            
            {/* Theme Mode */}
            <div className="setting-item">
              <label>{t('settings.theme')}</label>
              <select
                className="glass-input"
                value={theme.mode}
                onChange={(e) => updateTheme({ mode: e.target.value as any })}
              >
                <option value="auto">{t('settings.theme_auto')}</option>
                <option value="light">{t('settings.theme_light')}</option>
                <option value="dark">{t('settings.theme_dark')}</option>
              </select>
            </div>

            {/* Primary Color */}
            <div className="setting-item">
              <label>{t('settings.primary_color')}</label>
              <div className="color-picker-container">
                <input
                  type="color"
                  value={theme.primaryColor}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  className="color-picker"
                />
                <input
                  type="text"
                  value={theme.primaryColor}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  className="glass-input color-input"
                  placeholder="#667eea"
                />
              </div>
            </div>

            {/* Accent Color */}
            <div className="setting-item">
              <label>{t('settings.accent_color')}</label>
              <div className="color-picker-container">
                <input
                  type="color"
                  value={theme.accentColor}
                  onChange={(e) => handleColorChange('accent', e.target.value)}
                  className="color-picker"
                />
                <input
                  type="text"
                  value={theme.accentColor}
                  onChange={(e) => handleColorChange('accent', e.target.value)}
                  className="glass-input color-input"
                  placeholder="#f093fb"
                />
              </div>
            </div>

            {/* Glassmorphism Intensity */}
            <div className="setting-item">
              <label>
                {t('settings.glassmorphism_intensity')} ({theme.glassmorphismIntensity}%)
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={theme.glassmorphismIntensity}
                onChange={(e) => updateTheme({ glassmorphismIntensity: Number(e.target.value) })}
                className="glass-slider"
              />
            </div>

            {/* Font Size */}
            <div className="setting-item">
              <label>{t('settings.font_size')}</label>
              <select
                className="glass-input"
                value={theme.fontSize}
                onChange={(e) => updateTheme({ fontSize: e.target.value as any })}
              >
                <option value="small">{t('settings.font_size_small')}</option>
                <option value="medium">{t('settings.font_size_medium')}</option>
                <option value="large">{t('settings.font_size_large')}</option>
              </select>
            </div>

            {/* Art Mode */}
            <div className="setting-item">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={theme.artMode}
                  onChange={toggleArtMode}
                />
                <span>{t('settings.art_mode')}</span>
              </label>
              <small>{t('settings.art_mode_description')}</small>
            </div>

            {/* Preset Themes */}
            <div className="setting-item">
              <label>{t('settings.preset_themes')}</label>
              <div className="preset-themes">
                {presetThemes.map((preset, index) => (
                  <button
                    key={index}
                    className="preset-theme-button glass-button"
                    onClick={() => updateTheme(preset.theme)}
                    style={{
                      background: `linear-gradient(135deg, ${preset.theme.primaryColor}, ${preset.theme.accentColor})`
                    }}
                  >
                    {preset.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Theme Import/Export */}
            <div className="setting-item">
              <label>{t('settings.theme_backup')}</label>
              <div className="theme-backup-controls">
                <button className="glass-button" onClick={handleExportTheme}>
                  {t('settings.export_theme')}
                </button>
                <label className="glass-button import-button">
                  {t('settings.import_theme')}
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportTheme}
                    style={{ display: 'none' }}
                  />
                </label>
              </div>
            </div>

            {/* Reset Theme */}
            <div className="setting-item">
              <button className="glass-button reset-button" onClick={resetTheme}>
                {t('settings.reset_theme')}
              </button>
            </div>
          </div>
        )}

        {activeTab === 'language' && (
          <div className="settings-section">
            <h3>{t('settings.language')}</h3>
            
            <div className="setting-item">
              <label>{t('settings.interface_language')}</label>
              <select
                className="glass-input"
                value={currentLanguage}
                onChange={(e) => changeLanguage(e.target.value)}
              >
                {availableLanguages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.nativeName} ({lang.name})
                  </option>
                ))}
              </select>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.auto_detect_language')}</span>
              </label>
              <small>{t('settings.auto_detect_language_description')}</small>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.cultural_recipes')}</span>
              </label>
              <small>{t('settings.cultural_recipes_description')}</small>
            </div>
          </div>
        )}

        {activeTab === 'ai' && (
          <div className="settings-section">
            <h3>🤖 LLM Configuration</h3>

            {llmConfig && (
              <>
                <div className="setting-item">
                  <label>AI Provider</label>
                  <select
                    value={llmConfig.provider}
                    onChange={(e) => {
                      const newProvider = llmProviders.find(p => p.id === e.target.value);
                      setLlmConfig(prev => prev ? {
                        ...prev,
                        provider: e.target.value,
                        model: newProvider?.models[0]?.id || ''
                      } : null);
                    }}
                    className="glass-input"
                  >
                    {llmProviders.map(provider => (
                      <option key={provider.id} value={provider.id}>
                        {provider.name}
                      </option>
                    ))}
                  </select>
                  {llmProviders.find(p => p.id === llmConfig.provider) && (
                    <small className="provider-description">
                      {llmProviders.find(p => p.id === llmConfig.provider)?.description}
                    </small>
                  )}
                </div>

                <div className="setting-item">
                  <label>Model</label>
                  <select
                    value={llmConfig.model}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, model: e.target.value } : null)}
                    className="glass-input"
                  >
                    {llmProviders.find(p => p.id === llmConfig.provider)?.models.map(model => (
                      <option key={model.id} value={model.id}>
                        {model.name} - ${model.costPer1kTokens}/1k tokens
                      </option>
                    )) || []}
                  </select>
                </div>

                <div className="setting-item">
                  <label>API Key</label>
                  <input
                    type="password"
                    value={llmConfig.apiKey}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, apiKey: e.target.value } : null)}
                    placeholder="Enter your API key..."
                    className="glass-input"
                  />
                  <small>Get your API key from the provider's website</small>
                </div>

                <div className="setting-item">
                  <label>Temperature ({llmConfig.temperature})</label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={llmConfig.temperature}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, temperature: parseFloat(e.target.value) } : null)}
                    className="glass-slider"
                  />
                  <small>Higher = more creative, Lower = more focused</small>
                </div>

                <div className="setting-item">
                  <label>Max Tokens</label>
                  <input
                    type="number"
                    min="100"
                    max="8192"
                    value={llmConfig.maxTokens}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, maxTokens: parseInt(e.target.value) } : null)}
                    className="glass-input"
                  />
                </div>

                <div className="setting-item">
                  <label>System Prompt</label>
                  <textarea
                    value={llmConfig.systemPrompt}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, systemPrompt: e.target.value } : null)}
                    className="glass-input"
                    rows={4}
                    style={{ resize: 'vertical', minHeight: '100px' }}
                  />
                </div>

                <div className="settings-actions">
                  <button
                    onClick={handleTestConnection}
                    disabled={isTestingConnection || !llmConfig.apiKey}
                    className="glass-button"
                  >
                    {isTestingConnection ? 'Testing...' : 'Test Connection'}
                  </button>

                  <button
                    onClick={handleLLMConfigSave}
                    className="glass-button"
                    style={{ background: 'linear-gradient(45deg, #4CAF50, #45a049)' }}
                  >
                    Save Configuration
                  </button>
                </div>

                {testResult && (
                  <div className={`test-result ${testResult.includes('✅') ? 'success' : 'error'}`}>
                    {testResult}
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {activeTab === 'advanced' && (
          <div className="settings-section">
            <h3>⚙️ Advanced Recipe Settings</h3>

            {/* Target Audience */}
            <div className="setting-item">
              <label>🎯 Target Audience</label>
              <select
                value={targetAudience}
                onChange={(e) => setTargetAudience(e.target.value)}
                className="glass-input"
              >
                <option value="family">Family (All Ages)</option>
                <option value="adults">Adults Only</option>
                <option value="children">Children Friendly</option>
                <option value="elderly">Senior Friendly</option>
                <option value="teens">Teenagers</option>
                <option value="couples">Couples</option>
                <option value="single">Single Person</option>
              </select>
              <small>Recipes will be tailored for your target audience</small>
            </div>

            {/* Recipe Complexity */}
            <div className="setting-item">
              <label>📊 Recipe Complexity Preference</label>
              <select
                value={recipeComplexity}
                onChange={(e) => setRecipeComplexity(e.target.value)}
                className="glass-input"
              >
                <option value="simple">Simple (5-10 ingredients)</option>
                <option value="medium">Medium (10-15 ingredients)</option>
                <option value="complex">Complex (15+ ingredients)</option>
                <option value="gourmet">Gourmet (Professional level)</option>
              </select>
            </div>

            {/* Cultural Preferences */}
            <div className="setting-item">
              <label>🌍 Cultural Preferences</label>
              <div className="checkbox-grid">
                {['Mediterranean', 'Asian', 'Middle Eastern', 'Latin American', 'European', 'African', 'Indian', 'American'].map(culture => (
                  <label key={culture} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={culturalPreferences.includes(culture)}
                      onChange={() => handleArrayToggle(culturalPreferences, setCulturalPreferences, culture)}
                    />
                    {culture}
                  </label>
                ))}
              </div>
            </div>

            {/* Cooking Skill Level */}
            <div className="setting-item">
              <label>👨‍🍳 Cooking Skill Level</label>
              <select
                value={cookingSkillLevel}
                onChange={(e) => setCookingSkillLevel(e.target.value)}
                className="glass-input"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
                <option value="professional">Professional</option>
              </select>
            </div>

            {/* Preferred Cooking Methods */}
            <div className="setting-item">
              <label>🔥 Preferred Cooking Methods</label>
              <div className="checkbox-grid">
                {['Baking', 'Grilling', 'Frying', 'Steaming', 'Roasting', 'Slow Cooking', 'Pressure Cooking', 'Raw/No Cook'].map(method => (
                  <label key={method} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={preferredCookingMethods.includes(method)}
                      onChange={() => handleArrayToggle(preferredCookingMethods, setPreferredCookingMethods, method)}
                    />
                    {method}
                  </label>
                ))}
              </div>
            </div>

            {/* Kitchen Equipment */}
            <div className="setting-item">
              <label>🍳 Available Kitchen Equipment</label>
              <div className="checkbox-grid">
                {['Oven', 'Stovetop', 'Microwave', 'Air Fryer', 'Slow Cooker', 'Pressure Cooker', 'Blender', 'Food Processor', 'Stand Mixer', 'Grill'].map(equipment => (
                  <label key={equipment} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={kitchenEquipment.includes(equipment)}
                      onChange={() => handleArrayToggle(kitchenEquipment, setKitchenEquipment, equipment)}
                    />
                    {equipment}
                  </label>
                ))}
              </div>
            </div>

            {/* Time Constraints */}
            <div className="setting-item">
              <label>⏰ Time Constraints</label>
              <select
                value={timeConstraints}
                onChange={(e) => setTimeConstraints(e.target.value)}
                className="glass-input"
              >
                <option value="quick">Quick (Under 30 min)</option>
                <option value="moderate">Moderate (30-60 min)</option>
                <option value="flexible">Flexible (1-2 hours)</option>
                <option value="weekend">Weekend Projects (2+ hours)</option>
              </select>
            </div>

            {/* Budget Range */}
            <div className="setting-item">
              <label>💰 Budget Range</label>
              <select
                value={budgetRange}
                onChange={(e) => setBudgetRange(e.target.value)}
                className="glass-input"
              >
                <option value="budget">Budget Friendly</option>
                <option value="medium">Medium Range</option>
                <option value="premium">Premium Ingredients</option>
                <option value="luxury">Luxury/Gourmet</option>
              </select>
            </div>

            {/* Health Goals */}
            <div className="setting-item">
              <label>🏃‍♂️ Health Goals</label>
              <div className="checkbox-grid">
                {['Weight Loss', 'Muscle Building', 'Heart Healthy', 'Diabetes Friendly', 'High Protein', 'Low Sodium', 'Anti-Inflammatory', 'Energy Boosting'].map(goal => (
                  <label key={goal} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={healthGoals.includes(goal)}
                      onChange={() => handleArrayToggle(healthGoals, setHealthGoals, goal)}
                    />
                    {goal}
                  </label>
                ))}
              </div>
            </div>

            {/* Seasonal Preferences */}
            <div className="setting-item">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={seasonalPreferences}
                  onChange={(e) => setSeasonalPreferences(e.target.checked)}
                />
                <span>🌱 Prefer Seasonal Ingredients</span>
              </label>
              <small>Prioritize ingredients that are in season</small>
            </div>

            {/* Save Button */}
            <div className="setting-item">
              <button
                onClick={saveAdvancedSettings}
                className="glass-button"
                style={{ background: 'linear-gradient(45deg, #4CAF50, #45a049)', width: '100%' }}
              >
                💾 Save Advanced Settings
              </button>
            </div>
          </div>
        )}

        {activeTab === 'privacy' && (
          <div className="settings-section">
            <h3>{t('settings.privacy')}</h3>
            
            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.allow_data_collection')}</span>
              </label>
              <small>{t('settings.data_collection_description')}</small>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" defaultChecked />
                <span>{t('settings.share_anonymous_usage')}</span>
              </label>
              <small>{t('settings.anonymous_usage_description')}</small>
            </div>

            <div className="setting-item">
              <label className="checkbox-label">
                <input type="checkbox" />
                <span>{t('settings.enable_analytics')}</span>
              </label>
              <small>{t('settings.analytics_description')}</small>
            </div>

            <div className="setting-item">
              <button className="glass-button danger-button">
                {t('settings.clear_all_data')}
              </button>
              <small>{t('settings.clear_data_warning')}</small>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
