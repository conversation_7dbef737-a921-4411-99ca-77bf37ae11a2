// Customization Service - Advanced personalization and customization features
import { RealtimeUpdateService } from './RealtimeUpdateService';

export interface ThemeCustomization {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  borderRadius: number;
  glassmorphismIntensity: number;
  animationSpeed: number;
  fontFamily: string;
  fontSize: number;
}

export interface UICustomization {
  sidebarPosition: 'left' | 'right';
  sidebarWidth: number;
  compactMode: boolean;
  showAnimations: boolean;
  showTooltips: boolean;
  showNotifications: boolean;
  autoCollapse: boolean;
  keyboardShortcuts: Record<string, string>;
  customCSS: string;
}

export interface BehaviorCustomization {
  autoSaveRecipes: boolean;
  autoApplyFilters: boolean;
  smartSuggestions: boolean;
  contextualHelp: boolean;
  advancedMode: boolean;
  debugMode: boolean;
  telemetryEnabled: boolean;
  autoUpdates: boolean;
}

export interface PersonalizationData {
  favoriteIngredients: string[];
  dislikedIngredients: string[];
  cookingHistory: string[];
  preferredCuisines: string[];
  skillProgression: Record<string, number>;
  achievements: string[];
  customTags: string[];
  personalNotes: Record<string, string>;
}

export interface CustomizationProfile {
  id: string;
  name: string;
  description: string;
  theme: ThemeCustomization;
  ui: UICustomization;
  behavior: BehaviorCustomization;
  personalization: PersonalizationData;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export class CustomizationService {
  private static instance: CustomizationService;
  private currentProfile: CustomizationProfile | null = null;
  private profiles: Map<string, CustomizationProfile> = new Map();
  private realtimeService: RealtimeUpdateService;
  private readonly STORAGE_KEY = 'chefai_customization';

  private constructor() {
    this.realtimeService = RealtimeUpdateService.getInstance();
    this.loadProfiles();
    this.setupRealtimeListeners();
  }

  public static getInstance(): CustomizationService {
    if (!CustomizationService.instance) {
      CustomizationService.instance = new CustomizationService();
    }
    return CustomizationService.instance;
  }

  private getDefaultTheme(): ThemeCustomization {
    return {
      primaryColor: '#667eea',
      secondaryColor: '#764ba2',
      accentColor: '#f093fb',
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      textColor: '#ffffff',
      borderRadius: 12,
      glassmorphismIntensity: 0.8,
      animationSpeed: 1.0,
      fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif',
      fontSize: 14
    };
  }

  private getDefaultUI(): UICustomization {
    return {
      sidebarPosition: 'right',
      sidebarWidth: 450,
      compactMode: false,
      showAnimations: true,
      showTooltips: true,
      showNotifications: true,
      autoCollapse: false,
      keyboardShortcuts: {
        'toggle_interface': 'Ctrl+Shift+C',
        'generate_recipe': 'Ctrl+Enter',
        'save_recipe': 'Ctrl+S',
        'close_interface': 'Escape'
      },
      customCSS: ''
    };
  }

  private getDefaultBehavior(): BehaviorCustomization {
    return {
      autoSaveRecipes: true,
      autoApplyFilters: true,
      smartSuggestions: true,
      contextualHelp: true,
      advancedMode: false,
      debugMode: false,
      telemetryEnabled: true,
      autoUpdates: true
    };
  }

  private getDefaultPersonalization(): PersonalizationData {
    return {
      favoriteIngredients: [],
      dislikedIngredients: [],
      cookingHistory: [],
      preferredCuisines: [],
      skillProgression: {},
      achievements: [],
      customTags: [],
      personalNotes: {}
    };
  }

  public createDefaultProfile(): CustomizationProfile {
    return {
      id: 'default',
      name: 'Default Profile',
      description: 'Default ChefAI customization profile',
      theme: this.getDefaultTheme(),
      ui: this.getDefaultUI(),
      behavior: this.getDefaultBehavior(),
      personalization: this.getDefaultPersonalization(),
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
  }

  public async loadProfiles(): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEY]);
      const storedData = result[this.STORAGE_KEY];

      if (storedData && storedData.profiles) {
        // Load profiles
        for (const profileData of storedData.profiles) {
          const profile: CustomizationProfile = {
            ...profileData,
            createdAt: new Date(profileData.createdAt),
            updatedAt: new Date(profileData.updatedAt)
          };
          this.profiles.set(profile.id, profile);
        }

        // Set active profile
        const activeProfileId = storedData.activeProfileId || 'default';
        this.currentProfile = this.profiles.get(activeProfileId) || null;
      }

      // Create default profile if none exists
      if (this.profiles.size === 0) {
        const defaultProfile = this.createDefaultProfile();
        this.profiles.set(defaultProfile.id, defaultProfile);
        this.currentProfile = defaultProfile;
        await this.saveProfiles();
      }

      // Ensure we have an active profile
      if (!this.currentProfile) {
        this.currentProfile = this.profiles.values().next().value;
      }

      this.applyCurrentProfile();
    } catch (error) {
      console.error('Failed to load customization profiles:', error);
      // Fallback to default
      const defaultProfile = this.createDefaultProfile();
      this.profiles.set(defaultProfile.id, defaultProfile);
      this.currentProfile = defaultProfile;
    }
  }

  public async saveProfiles(): Promise<void> {
    try {
      const profilesArray = Array.from(this.profiles.values());
      const dataToSave = {
        profiles: profilesArray,
        activeProfileId: this.currentProfile?.id || 'default',
        lastUpdated: new Date().toISOString()
      };

      await chrome.storage.local.set({
        [this.STORAGE_KEY]: dataToSave
      });

      this.realtimeService.emit('customization_changed', {
        activeProfile: this.currentProfile?.id,
        profileCount: this.profiles.size
      }, 'customization_service');
    } catch (error) {
      console.error('Failed to save customization profiles:', error);
      throw error;
    }
  }

  public async createProfile(
    name: string,
    description: string,
    baseProfile?: CustomizationProfile
  ): Promise<CustomizationProfile> {
    const id = `profile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const base = baseProfile || this.currentProfile || this.createDefaultProfile();

    const newProfile: CustomizationProfile = {
      id,
      name,
      description,
      theme: { ...base.theme },
      ui: { ...base.ui },
      behavior: { ...base.behavior },
      personalization: { ...base.personalization },
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: false
    };

    this.profiles.set(id, newProfile);
    await this.saveProfiles();

    return newProfile;
  }

  public async updateProfile(
    profileId: string,
    updates: Partial<Omit<CustomizationProfile, 'id' | 'createdAt'>>
  ): Promise<void> {
    const profile = this.profiles.get(profileId);
    if (!profile) {
      throw new Error(`Profile ${profileId} not found`);
    }

    Object.assign(profile, updates, {
      updatedAt: new Date()
    });

    await this.saveProfiles();

    // If updating current profile, apply changes
    if (this.currentProfile?.id === profileId) {
      this.applyCurrentProfile();
    }
  }

  public async deleteProfile(profileId: string): Promise<void> {
    if (profileId === 'default') {
      throw new Error('Cannot delete default profile');
    }

    const profile = this.profiles.get(profileId);
    if (!profile) {
      throw new Error(`Profile ${profileId} not found`);
    }

    this.profiles.delete(profileId);

    // If deleting current profile, switch to default
    if (this.currentProfile?.id === profileId) {
      this.currentProfile = this.profiles.get('default') || this.createDefaultProfile();
      this.applyCurrentProfile();
    }

    await this.saveProfiles();
  }

  public async setActiveProfile(profileId: string): Promise<void> {
    const profile = this.profiles.get(profileId);
    if (!profile) {
      throw new Error(`Profile ${profileId} not found`);
    }

    // Deactivate current profile
    if (this.currentProfile) {
      this.currentProfile.isActive = false;
    }

    // Activate new profile
    profile.isActive = true;
    this.currentProfile = profile;

    this.applyCurrentProfile();
    await this.saveProfiles();
  }

  private applyCurrentProfile(): void {
    if (!this.currentProfile) return;

    try {
      this.applyTheme(this.currentProfile.theme);
      this.applyUISettings(this.currentProfile.ui);
      this.applyBehaviorSettings(this.currentProfile.behavior);
    } catch (error) {
      console.error('Failed to apply profile:', error);
    }
  }

  private applyTheme(theme: ThemeCustomization): void {
    const root = document.documentElement;
    
    root.style.setProperty('--chefai-primary-color', theme.primaryColor);
    root.style.setProperty('--chefai-secondary-color', theme.secondaryColor);
    root.style.setProperty('--chefai-accent-color', theme.accentColor);
    root.style.setProperty('--chefai-background-color', theme.backgroundColor);
    root.style.setProperty('--chefai-text-color', theme.textColor);
    root.style.setProperty('--chefai-border-radius', `${theme.borderRadius}px`);
    root.style.setProperty('--chefai-glassmorphism-intensity', theme.glassmorphismIntensity.toString());
    root.style.setProperty('--chefai-animation-speed', theme.animationSpeed.toString());
    root.style.setProperty('--chefai-font-family', theme.fontFamily);
    root.style.setProperty('--chefai-font-size', `${theme.fontSize}px`);
  }

  private applyUISettings(ui: UICustomization): void {
    const root = document.documentElement;
    
    root.style.setProperty('--chefai-sidebar-position', ui.sidebarPosition);
    root.style.setProperty('--chefai-sidebar-width', `${ui.sidebarWidth}px`);
    
    // Apply CSS classes
    document.body.classList.toggle('chefai-compact-mode', ui.compactMode);
    document.body.classList.toggle('chefai-no-animations', !ui.showAnimations);
    document.body.classList.toggle('chefai-no-tooltips', !ui.showTooltips);
    
    // Apply custom CSS
    if (ui.customCSS) {
      this.injectCustomCSS(ui.customCSS);
    }
  }

  private applyBehaviorSettings(behavior: BehaviorCustomization): void {
    // These settings are used by other services
    // Emit event so other services can react
    this.realtimeService.emit('behavior_settings_changed', behavior, 'customization_service');
  }

  private injectCustomCSS(css: string): void {
    // Remove existing custom CSS
    const existingStyle = document.getElementById('chefai-custom-css');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Inject new custom CSS
    if (css.trim()) {
      const style = document.createElement('style');
      style.id = 'chefai-custom-css';
      style.textContent = css;
      document.head.appendChild(style);
    }
  }

  private setupRealtimeListeners(): void {
    this.realtimeService.subscribe(
      'customization_service',
      ['settings_changed', 'profile_imported'],
      (event) => {
        if (event.type === 'profile_imported') {
          this.loadProfiles();
        }
      },
      10
    );
  }

  // Public getters
  public getCurrentProfile(): CustomizationProfile | null {
    return this.currentProfile;
  }

  public getAllProfiles(): CustomizationProfile[] {
    return Array.from(this.profiles.values());
  }

  public getProfile(profileId: string): CustomizationProfile | null {
    return this.profiles.get(profileId) || null;
  }

  // Export/Import functionality
  public exportProfile(profileId: string): string {
    const profile = this.profiles.get(profileId);
    if (!profile) {
      throw new Error(`Profile ${profileId} not found`);
    }

    return JSON.stringify(profile, null, 2);
  }

  public async importProfile(profileData: string): Promise<CustomizationProfile> {
    try {
      const parsed = JSON.parse(profileData);
      
      // Validate and sanitize
      const profile: CustomizationProfile = {
        ...parsed,
        id: `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: false
      };

      this.profiles.set(profile.id, profile);
      await this.saveProfiles();

      this.realtimeService.emit('profile_imported', profile, 'customization_service');

      return profile;
    } catch (error) {
      throw new Error(`Failed to import profile: ${error.message}`);
    }
  }

  public async resetToDefaults(): Promise<void> {
    const defaultProfile = this.createDefaultProfile();
    this.profiles.clear();
    this.profiles.set('default', defaultProfile);
    this.currentProfile = defaultProfile;
    
    this.applyCurrentProfile();
    await this.saveProfiles();
  }
}
