{"name": "chefai-advanced-recipe-generator", "version": "2.0.0", "description": "Professional AI-powered recipe generator with advanced dashboard, multiple LLM support, and glassmorphism UI", "main": "background.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development", "watch": "webpack --mode development --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean && npm run type-check", "analyze": "webpack-bundle-analyzer dist/static/js/*.js", "start": "npm run dev", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,css,md}\""}, "keywords": ["chrome-extension", "ai", "recipe-generator", "cooking", "food", "llm", "openai", "gemini", "openrouter", "glassmorphism", "react", "typescript"], "author": "ChefAI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chefai/advanced-recipe-generator.git"}, "bugs": {"url": "https://github.com/chefai/advanced-recipe-generator/issues"}, "homepage": "https://github.com/chefai/advanced-recipe-generator#readme", "devDependencies": {"@types/chrome": "^0.0.246", "@types/jest": "^29.5.5", "@types/node": "^20.6.3", "@types/react": "^18.2.22", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "css-loader": "^6.8.1", "eslint": "^8.49.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "mini-css-extract-plugin": "^2.7.6", "prettier": "^3.0.3", "rimraf": "^5.0.1", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.4"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/tests/setup.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/tests/**/*"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}, "eslintConfig": {"extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "react/react-in-jsx-scope": "off", "react/prop-types": "off"}, "settings": {"react": {"version": "detect"}}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}, "browserslist": ["last 2 Chrome versions"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}