// Smart Notification Manager for ChefAI
class NotificationManager {
  constructor() {
    this.notifications = new Map();
    this.queue = [];
    this.isInitialized = false;
    this.settings = {
      enabled: true,
      position: 'top-right',
      duration: 5000,
      maxVisible: 3,
      sound: false,
      groupSimilar: true
    };
    
    // Notification types
    this.TYPES = {
      SUCCESS: 'success',
      ERROR: 'error',
      WARNING: 'warning',
      INFO: 'info',
      RECIPE: 'recipe',
      UPDATE: 'update',
      SYNC: 'sync'
    };
    
    // Priority levels
    this.PRIORITY = {
      LOW: 1,
      NORMAL: 2,
      HIGH: 3,
      URGENT: 4
    };
  }

  // Initialize notification manager
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Load settings
      await this.loadSettings();
      
      // Create notification container
      this.createNotificationContainer();
      
      // Set up real-time listeners
      this.setupRealtimeListeners();
      
      this.isInitialized = true;
      console.log('NotificationManager initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize NotificationManager:', error);
    }
  }

  // Load notification settings
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['chefai_notification_settings']);
      
      if (result.chefai_notification_settings) {
        this.settings = { ...this.settings, ...result.chefai_notification_settings };
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  }

  // Save notification settings
  async saveSettings() {
    try {
      await chrome.storage.local.set({
        chefai_notification_settings: this.settings
      });
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  }

  // Create notification container
  createNotificationContainer() {
    if (typeof document === 'undefined') return;
    
    // Remove existing container
    const existing = document.getElementById('chefai-notifications');
    if (existing) {
      existing.remove();
    }

    const container = document.createElement('div');
    container.id = 'chefai-notifications';
    container.className = `chefai-notification-container ${this.settings.position}`;
    
    // Add styles
    container.style.cssText = `
      position: fixed;
      z-index: 10000;
      pointer-events: none;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ${this.getPositionStyles()}
    `;
    
    document.body.appendChild(container);
  }

  // Get position styles based on settings
  getPositionStyles() {
    const positions = {
      'top-right': 'top: 20px; right: 20px;',
      'top-left': 'top: 20px; left: 20px;',
      'bottom-right': 'bottom: 20px; right: 20px;',
      'bottom-left': 'bottom: 20px; left: 20px;',
      'top-center': 'top: 20px; left: 50%; transform: translateX(-50%);',
      'bottom-center': 'bottom: 20px; left: 50%; transform: translateX(-50%);'
    };
    
    return positions[this.settings.position] || positions['top-right'];
  }

  // Set up real-time listeners
  setupRealtimeListeners() {
    if (window.realtimeManager) {
      // Listen for various events
      window.realtimeManager.on('api_key_changed', (data) => {
        this.show({
          type: this.TYPES.SUCCESS,
          title: 'API Key Updated',
          message: 'Your API key has been updated successfully',
          priority: this.PRIORITY.NORMAL
        });
      });

      window.realtimeManager.on('recipe_generated', (data) => {
        this.show({
          type: this.TYPES.RECIPE,
          title: 'Recipe Generated',
          message: `New recipe: ${data.title || 'Delicious Recipe'}`,
          priority: this.PRIORITY.NORMAL,
          actions: [
            {
              label: 'View',
              action: () => this.openRecipe(data)
            }
          ]
        });
      });

      window.realtimeManager.on('connection_status', (data) => {
        if (data.status === 'error') {
          this.show({
            type: this.TYPES.ERROR,
            title: 'Connection Error',
            message: data.error || 'Failed to connect to services',
            priority: this.PRIORITY.HIGH,
            persistent: true
          });
        } else if (data.status === 'connected') {
          this.show({
            type: this.TYPES.SUCCESS,
            title: 'Connected',
            message: 'Successfully connected to ChefAI services',
            priority: this.PRIORITY.LOW
          });
        }
      });

      window.realtimeManager.on('settings_updated', (data) => {
        this.show({
          type: this.TYPES.INFO,
          title: 'Settings Updated',
          message: 'Your settings have been synchronized',
          priority: this.PRIORITY.LOW
        });
      });
    }
  }

  // Show notification
  show(options) {
    if (!this.settings.enabled) return null;

    const notification = this.createNotification(options);
    
    // Check for similar notifications if grouping is enabled
    if (this.settings.groupSimilar) {
      const similar = this.findSimilarNotification(notification);
      if (similar) {
        this.updateNotification(similar.id, notification);
        return similar.id;
      }
    }

    // Add to queue if too many visible
    const visibleCount = Array.from(this.notifications.values())
      .filter(n => n.visible).length;
      
    if (visibleCount >= this.settings.maxVisible) {
      this.queue.push(notification);
      return notification.id;
    }

    // Show immediately
    this.displayNotification(notification);
    return notification.id;
  }

  // Create notification object
  createNotification(options) {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const notification = {
      id,
      type: options.type || this.TYPES.INFO,
      title: options.title || '',
      message: options.message || '',
      priority: options.priority || this.PRIORITY.NORMAL,
      duration: options.duration || this.settings.duration,
      persistent: options.persistent || false,
      actions: options.actions || [],
      timestamp: Date.now(),
      visible: false,
      element: null
    };

    this.notifications.set(id, notification);
    return notification;
  }

  // Find similar notification
  findSimilarNotification(notification) {
    for (const existing of this.notifications.values()) {
      if (existing.visible && 
          existing.type === notification.type &&
          existing.title === notification.title) {
        return existing;
      }
    }
    return null;
  }

  // Update existing notification
  updateNotification(id, newData) {
    const notification = this.notifications.get(id);
    if (!notification) return;

    // Update data
    Object.assign(notification, newData);
    notification.timestamp = Date.now();

    // Update DOM element
    if (notification.element) {
      this.updateNotificationElement(notification);
    }
  }

  // Display notification
  displayNotification(notification) {
    const container = document.getElementById('chefai-notifications');
    if (!container) return;

    // Create notification element
    const element = this.createNotificationElement(notification);
    notification.element = element;
    notification.visible = true;

    // Add to container
    container.appendChild(element);

    // Animate in
    requestAnimationFrame(() => {
      element.style.transform = 'translateX(0)';
      element.style.opacity = '1';
    });

    // Auto-hide if not persistent
    if (!notification.persistent && notification.duration > 0) {
      setTimeout(() => {
        this.hide(notification.id);
      }, notification.duration);
    }

    // Play sound if enabled
    if (this.settings.sound) {
      this.playNotificationSound(notification.type);
    }
  }

  // Create notification DOM element
  createNotificationElement(notification) {
    const element = document.createElement('div');
    element.className = `chefai-notification chefai-notification-${notification.type}`;
    element.style.cssText = `
      background: var(--chefai-color-surface, #34495e);
      border: 1px solid var(--chefai-color-border, #4a5f7a);
      border-radius: var(--chefai-radius-md, 6px);
      padding: 16px;
      margin-bottom: 12px;
      min-width: 300px;
      max-width: 400px;
      box-shadow: var(--chefai-shadow-lg, 0 8px 16px rgba(0,0,0,0.2));
      transform: translateX(100%);
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      pointer-events: auto;
      position: relative;
    `;

    // Add type-specific styling
    const typeColors = {
      [this.TYPES.SUCCESS]: '#27ae60',
      [this.TYPES.ERROR]: '#e74c3c',
      [this.TYPES.WARNING]: '#f39c12',
      [this.TYPES.INFO]: '#3498db',
      [this.TYPES.RECIPE]: '#8e44ad',
      [this.TYPES.UPDATE]: '#2ecc71',
      [this.TYPES.SYNC]: '#34495e'
    };

    const typeColor = typeColors[notification.type] || typeColors[this.TYPES.INFO];
    element.style.borderLeftColor = typeColor;
    element.style.borderLeftWidth = '4px';

    // Create content
    element.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: flex-start;">
        <div style="flex: 1;">
          ${notification.title ? `
            <div style="
              font-weight: 600;
              font-size: 14px;
              color: var(--chefai-color-text, #ecf0f1);
              margin-bottom: 4px;
            ">${notification.title}</div>
          ` : ''}
          ${notification.message ? `
            <div style="
              font-size: 13px;
              color: var(--chefai-color-text-secondary, #bdc3c7);
              line-height: 1.4;
            ">${notification.message}</div>
          ` : ''}
          ${notification.actions.length > 0 ? `
            <div style="margin-top: 12px; display: flex; gap: 8px;">
              ${notification.actions.map(action => `
                <button class="chefai-notification-action" data-action="${action.label}" style="
                  background: ${typeColor};
                  border: none;
                  color: white;
                  padding: 6px 12px;
                  border-radius: 4px;
                  font-size: 12px;
                  cursor: pointer;
                  transition: opacity 0.2s;
                ">
                  ${action.label}
                </button>
              `).join('')}
            </div>
          ` : ''}
        </div>
        <button class="chefai-notification-close" style="
          background: none;
          border: none;
          color: var(--chefai-color-text-secondary, #bdc3c7);
          font-size: 16px;
          cursor: pointer;
          padding: 0;
          margin-left: 12px;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        ">×</button>
      </div>
    `;

    // Add event listeners
    const closeBtn = element.querySelector('.chefai-notification-close');
    closeBtn.addEventListener('click', () => this.hide(notification.id));

    // Add action listeners
    notification.actions.forEach(action => {
      const btn = element.querySelector(`[data-action="${action.label}"]`);
      if (btn) {
        btn.addEventListener('click', () => {
          action.action();
          this.hide(notification.id);
        });
      }
    });

    return element;
  }

  // Update notification element
  updateNotificationElement(notification) {
    if (!notification.element) return;

    // Update content (simplified version)
    const messageEl = notification.element.querySelector('[style*="line-height"]');
    if (messageEl) {
      messageEl.textContent = notification.message;
    }
  }

  // Hide notification
  hide(id) {
    const notification = this.notifications.get(id);
    if (!notification || !notification.visible) return;

    const element = notification.element;
    if (element) {
      // Animate out
      element.style.transform = 'translateX(100%)';
      element.style.opacity = '0';
      
      setTimeout(() => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      }, 300);
    }

    notification.visible = false;
    this.notifications.delete(id);

    // Show next in queue
    this.processQueue();
  }

  // Process notification queue
  processQueue() {
    if (this.queue.length === 0) return;

    const visibleCount = Array.from(this.notifications.values())
      .filter(n => n.visible).length;

    if (visibleCount < this.settings.maxVisible) {
      const next = this.queue.shift();
      this.displayNotification(next);
    }
  }

  // Play notification sound
  playNotificationSound(type) {
    // Simple beep sound using Web Audio API
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = type === this.TYPES.ERROR ? 300 : 600;
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
      // Ignore audio errors
    }
  }

  // Update settings
  async updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    await this.saveSettings();

    // Recreate container if position changed
    if (newSettings.position) {
      this.createNotificationContainer();
    }
  }

  // Clear all notifications
  clearAll() {
    this.notifications.forEach((notification) => {
      if (notification.visible) {
        this.hide(notification.id);
      }
    });
    this.queue = [];
  }

  // Get notification history
  getHistory(limit = 50) {
    return Array.from(this.notifications.values())
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }
}

// Export for use in different contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NotificationManager;
} else if (typeof window !== 'undefined') {
  window.NotificationManager = NotificationManager;
}

// Create global instance
const notificationManager = new NotificationManager();

// Auto-initialize
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      notificationManager.initialize().catch(console.error);
    });
  } else {
    notificationManager.initialize().catch(console.error);
  }
}
