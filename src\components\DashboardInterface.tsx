import React, { useState, useEffect } from 'react';
import { AdvancedSettingsPanel } from './AdvancedSettingsPanel';
import { CustomizationPanel } from './CustomizationPanel';
import { SettingsPanel } from './SettingsPanel';
import { SidebarInterface } from './SidebarInterface';
import { LLMService } from '../services/LLMService';
import { AdvancedSettingsService } from '../services/AdvancedSettingsService';
import { RecipeService } from '../services/RecipeService';
import { Recipe } from '../types';

interface DashboardInterfaceProps {
  isVisible: boolean;
  onClose: () => void;
  initialTab?: 'generator' | 'settings' | 'advanced' | 'customization';
}

export const DashboardInterface: React.FC<DashboardInterfaceProps> = ({
  isVisible,
  onClose,
  initialTab = 'generator'
}) => {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [isGenerating, setIsGenerating] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<Record<string, boolean>>({});
  const [recentRecipes, setRecentRecipes] = useState<Recipe[]>([]);
  const [systemStatus, setSystemStatus] = useState({
    llmConfigured: false,
    settingsLoaded: false,
    apiHealthy: false
  });

  const llmService = LLMService.getInstance();
  const advancedSettingsService = AdvancedSettingsService.getInstance();
  const recipeService = RecipeService.getInstance();

  useEffect(() => {
    if (isVisible) {
      initializeDashboard();
    }
  }, [isVisible]);

  const initializeDashboard = async () => {
    try {
      // Load system status
      await checkSystemStatus();
      
      // Load recent recipes
      await loadRecentRecipes();
      
      // Check API health
      await checkAPIHealth();
    } catch (error) {
      console.error('Failed to initialize dashboard:', error);
    }
  };

  const checkSystemStatus = async () => {
    try {
      const llmConfig = llmService.getConfiguration();
      const settings = await advancedSettingsService.loadSettings();
      
      setSystemStatus({
        llmConfigured: !!(llmConfig && llmConfig.apiKey),
        settingsLoaded: !!settings,
        apiHealthy: false // Will be updated by checkAPIHealth
      });
    } catch (error) {
      console.error('Failed to check system status:', error);
    }
  };

  const checkAPIHealth = async () => {
    try {
      const config = llmService.getConfiguration();
      if (config && config.apiKey) {
        const testResult = await llmService.testConnection();
        setSystemStatus(prev => ({
          ...prev,
          apiHealthy: testResult.success
        }));
      }
    } catch (error) {
      console.error('Failed to check API health:', error);
    }
  };

  const loadRecentRecipes = async () => {
    try {
      const recipes = await recipeService.getRecipes({
        filter: { source: 'AI Generated' },
        sortBy: 'date',
        sortOrder: 'desc',
        limit: 5
      });
      setRecentRecipes(recipes);
    } catch (error) {
      console.error('Failed to load recent recipes:', error);
    }
  };

  const handleRecipeGenerated = async (recipe: Recipe) => {
    try {
      await recipeService.saveRecipe(recipe);
      await loadRecentRecipes();
    } catch (error) {
      console.error('Failed to save generated recipe:', error);
    }
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab as any);
  };

  const tabs = [
    {
      id: 'generator',
      name: 'Recipe Generator',
      icon: '✨',
      description: 'Generate AI-powered recipes'
    },
    {
      id: 'settings',
      name: 'LLM Settings',
      icon: '🤖',
      description: 'Configure AI models and APIs'
    },
    {
      id: 'advanced',
      name: 'Advanced Settings',
      icon: '⚙️',
      description: 'Detailed preferences and options'
    },
    {
      id: 'customization',
      name: 'Customization',
      icon: '🎨',
      description: 'Personalize your experience'
    }
  ];

  if (!isVisible) return null;

  return (
    <div className="dashboard-interface">
      <div className="dashboard-overlay" onClick={onClose}></div>
      <div className="dashboard-content">
        {/* Dashboard Header */}
        <div className="dashboard-header">
          <div className="header-info">
            <h1 className="dashboard-title">🍳 ChefAI Dashboard</h1>
            <p className="dashboard-subtitle">Advanced Recipe Generation & Management</p>
          </div>
          
          <div className="header-status">
            <div className="status-indicators">
              <div className={`status-indicator ${systemStatus.llmConfigured ? 'active' : 'inactive'}`}>
                <span className="status-icon">🤖</span>
                <span className="status-text">LLM</span>
              </div>
              <div className={`status-indicator ${systemStatus.apiHealthy ? 'active' : 'inactive'}`}>
                <span className="status-icon">🌐</span>
                <span className="status-text">API</span>
              </div>
              <div className={`status-indicator ${systemStatus.settingsLoaded ? 'active' : 'inactive'}`}>
                <span className="status-icon">⚙️</span>
                <span className="status-text">Settings</span>
              </div>
            </div>
            
            <button className="dashboard-close" onClick={onClose}>×</button>
          </div>
        </div>

        {/* Dashboard Navigation */}
        <div className="dashboard-navigation">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => handleTabChange(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              <div className="tab-info">
                <span className="tab-name">{tab.name}</span>
                <span className="tab-description">{tab.description}</span>
              </div>
            </button>
          ))}
        </div>

        {/* Dashboard Content */}
        <div className="dashboard-main">
          {activeTab === 'generator' && (
            <div className="dashboard-section">
              <SidebarInterface
                isVisible={true}
                onClose={() => {}}
                onRecipeGenerated={handleRecipeGenerated}
              />
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="dashboard-section">
              <SettingsPanel onClose={() => {}} />
            </div>
          )}

          {activeTab === 'advanced' && (
            <div className="dashboard-section">
              <AdvancedSettingsPanel onClose={() => {}} />
            </div>
          )}

          {activeTab === 'customization' && (
            <div className="dashboard-section">
              <CustomizationPanel onClose={() => {}} />
            </div>
          )}
        </div>

        {/* Dashboard Footer */}
        <div className="dashboard-footer">
          <div className="footer-stats">
            <div className="stat-item">
              <span className="stat-value">{recentRecipes.length}</span>
              <span className="stat-label">Recent Recipes</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{systemStatus.apiHealthy ? 'Online' : 'Offline'}</span>
              <span className="stat-label">API Status</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{systemStatus.llmConfigured ? 'Ready' : 'Setup Required'}</span>
              <span className="stat-label">System Status</span>
            </div>
          </div>
          
          <div className="footer-actions">
            <button 
              className="footer-button"
              onClick={checkAPIHealth}
              disabled={!systemStatus.llmConfigured}
            >
              🔄 Refresh Status
            </button>
            <button 
              className="footer-button"
              onClick={loadRecentRecipes}
            >
              📚 Reload Recipes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
