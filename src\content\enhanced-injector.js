// Enhanced Content Script - Integrates ChefAI Dashboard with web pages
class EnhancedChefAIInjector {
  constructor() {
    this.state = {
      isVisible: false,
      isInitialized: false,
      container: null,
      floatingButton: null,
      currentMode: 'dashboard'
    };
    
    this.init();
  }

  async init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupInterface());
    } else {
      this.setupInterface();
    }

    // Listen for messages from extension
    this.setupMessageListeners();
    
    // Setup keyboard shortcuts
    this.setupKeyboardShortcuts();
  }

  async setupInterface() {
    try {
      // Load and initialize services first
      await this.loadServices();

      this.createFloatingButton();
      this.loadStyles();

      // Set up real-time updates
      this.setupRealtimeUpdates();

      this.state.isInitialized = true;
      console.log('ChefAI interface initialized successfully');
    } catch (error) {
      console.error('Failed to setup ChefAI interface:', error);
    }
  }

  // Load required services
  async loadServices() {
    try {
      // Load service scripts if not already loaded
      if (!window.RealtimeManager) {
        await this.loadScript(chrome.runtime.getURL('src/services/realtime-manager.js'));
      }

      if (!window.CustomizationManager) {
        await this.loadScript(chrome.runtime.getURL('src/services/customization-manager.js'));
      }

      if (!window.NotificationManager) {
        await this.loadScript(chrome.runtime.getURL('src/services/notification-manager.js'));
      }

      // Initialize services
      if (window.realtimeManager) {
        await window.realtimeManager.initialize();
      }

      if (window.customizationManager) {
        await window.customizationManager.initialize();
      }

      if (window.notificationManager) {
        await window.notificationManager.initialize();
      }

    } catch (error) {
      console.error('Error loading services:', error);
    }
  }

  // Load script dynamically
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Set up real-time updates
  setupRealtimeUpdates() {
    if (window.realtimeManager) {
      // Listen for theme changes
      window.realtimeManager.on('theme_changed', (data) => {
        this.applyThemeUpdates(data);
      });

      // Listen for settings updates
      window.realtimeManager.on('settings_updated', (data) => {
        this.applySettingsUpdates(data);
      });

      // Listen for API key changes
      window.realtimeManager.on('api_key_changed', (data) => {
        this.handleAPIKeyChange(data);
      });
    }
  }

  createFloatingButton() {
    // Remove existing button if any
    if (this.state.floatingButton) {
      this.state.floatingButton.remove();
    }

    this.state.floatingButton = document.createElement('div');
    this.state.floatingButton.id = 'chefai-floating-button';
    this.state.floatingButton.innerHTML = '🍳';
    this.state.floatingButton.title = 'Open ChefAI Dashboard (Ctrl+Shift+C)';

    // Apply enhanced styles similar to the image
    Object.assign(this.state.floatingButton.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '56px',
      height: '56px',
      borderRadius: '8px',
      background: '#3498db',
      color: 'white',
      fontSize: '24px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: '9999',
      boxShadow: '0 4px 12px rgba(52, 152, 219, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      transition: 'all 0.2s ease',
      userSelect: 'none',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    });

    // Add hover effects
    this.state.floatingButton.addEventListener('mouseenter', () => {
      if (this.state.floatingButton) {
        Object.assign(this.state.floatingButton.style, {
          transform: 'scale(1.05)',
          boxShadow: '0 6px 16px rgba(52, 152, 219, 0.4)',
          background: '#2980b9'
        });
      }
    });

    this.state.floatingButton.addEventListener('mouseleave', () => {
      if (this.state.floatingButton) {
        Object.assign(this.state.floatingButton.style, {
          transform: 'scale(1)',
          boxShadow: '0 4px 12px rgba(52, 152, 219, 0.3)',
          background: '#3498db'
        });
      }
    });

    // Add click handler
    this.state.floatingButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.toggleInterface();
    });

    // Add to page
    document.body.appendChild(this.state.floatingButton);
  }

  loadStyles() {
    const stylesheets = [
      'src/styles/sidebar.css',
      'src/styles/dashboard.css',
      'src/styles/customization.css'
    ];

    stylesheets.forEach(href => {
      if (!document.querySelector(`link[href*="${href}"]`)) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = chrome.runtime.getURL(href);
        document.head.appendChild(link);
      }
    });
  }

  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      try {
        switch (message.action) {
          case 'toggleInterface':
            this.toggleInterface();
            sendResponse({ success: true });
            break;
          case 'showInterface':
            this.showInterface(message.mode || 'dashboard');
            sendResponse({ success: true });
            break;
          case 'hideInterface':
            this.hideInterface();
            sendResponse({ success: true });
            break;
          case 'switchMode':
            this.switchMode(message.mode);
            sendResponse({ success: true });
            break;
          case 'getStatus':
            sendResponse({
              success: true,
              status: {
                isVisible: this.state.isVisible,
                isInitialized: this.state.isInitialized,
                currentMode: this.state.currentMode
              }
            });
            break;
          default:
            sendResponse({ success: false, error: 'Unknown action' });
        }
      } catch (error) {
        console.error('Error handling message:', error);
        sendResponse({ success: false, error: error.message });
      }
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+C to toggle interface
      if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
        e.preventDefault();
        this.toggleInterface();
      }
      
      // Escape to close interface
      if (e.code === 'Escape' && this.state.isVisible) {
        e.preventDefault();
        this.hideInterface();
      }
    });
  }

  toggleInterface() {
    if (this.state.isVisible) {
      this.hideInterface();
    } else {
      this.showInterface();
    }
  }

  showInterface(mode = 'dashboard') {
    if (!this.state.isInitialized) {
      console.warn('ChefAI interface not initialized');
      return;
    }

    try {
      // Create container if it doesn't exist
      if (!this.state.container) {
        this.createContainer();
      }

      // Update mode
      this.state.currentMode = mode;

      // Create simple interface
      this.renderSimpleInterface();

      // Show interface
      this.state.isVisible = true;
      
      // Hide floating button when interface is open
      if (this.state.floatingButton) {
        this.state.floatingButton.style.display = 'none';
      }

      // Add body class for styling
      document.body.classList.add('chefai-interface-open');

    } catch (error) {
      console.error('Failed to show ChefAI interface:', error);
    }
  }

  hideInterface() {
    try {
      if (this.state.container) {
        this.state.container.remove();
        this.state.container = null;
      }

      this.state.isVisible = false;

      // Show floating button
      if (this.state.floatingButton) {
        this.state.floatingButton.style.display = 'flex';
      }

      // Remove body class
      document.body.classList.remove('chefai-interface-open');

    } catch (error) {
      console.error('Failed to hide ChefAI interface:', error);
    }
  }

  switchMode(mode) {
    if (this.state.currentMode !== mode) {
      this.state.currentMode = mode;
      if (this.state.isVisible) {
        this.renderSimpleInterface();
      }
    }
  }

  createContainer() {
    this.state.container = document.createElement('div');
    this.state.container.id = 'chefai-app-container';
    this.state.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10000;
      pointer-events: auto;
    `;
    document.body.appendChild(this.state.container);
  }

  renderSimpleInterface() {
    if (!this.state.container) return;

    // Create sidebar interface similar to the image
    this.state.container.innerHTML = `
      <div class="chefai-sidebar" style="
        position: fixed;
        top: 0;
        right: 0;
        width: 400px;
        height: 100vh;
        background: #2c3e50;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transform: translateX(100%);
        transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        z-index: 10001;
        box-shadow: -5px 0 20px rgba(0, 0, 0, 0.3);
        border-left: 1px solid #34495e;
        overflow-y: auto;
      ">
        <!-- Header -->
        <div style="
          padding: 16px 20px;
          background: #34495e;
          border-bottom: 1px solid #4a5f7a;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: sticky;
          top: 0;
          z-index: 10;
        ">
          <h2 style="
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 8px;
          ">
            <span style="color: #3498db;">🍳</span>
            ChefAI: AI Web Agent & Automation
          </h2>

          <div style="display: flex; align-items: center; gap: 8px;">
            <button id="chefai-dashboard-btn" style="
              background: #3498db;
              border: none;
              color: white;
              padding: 6px 12px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 12px;
              font-weight: 500;
              transition: all 0.2s ease;
              display: flex;
              align-items: center;
              gap: 4px;
            " title="Open Dashboard">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
              </svg>
              Dashboard
            </button>

            <button id="chefai-close-btn" style="
              background: transparent;
              border: none;
              color: #bdc3c7;
              font-size: 18px;
              cursor: pointer;
              padding: 6px;
              width: 30px;
              height: 30px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 4px;
              transition: all 0.2s ease;
            ">×</button>
          </div>
        </div>

        <!-- Main Content -->
        <div style="padding: 24px 20px;">
          <!-- Chat Input Area -->
          <div style="
            background: #34495e;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border: 1px solid #4a5f7a;
          ">
            <div style="
              background: #2c3e50;
              border-radius: 6px;
              padding: 12px;
              margin-bottom: 12px;
              border: 1px solid #34495e;
            ">
              <input type="text" id="chefai-input" placeholder="What can I help you with?" style="
                width: 100%;
                background: transparent;
                border: none;
                color: #ecf0f1;
                font-size: 14px;
                outline: none;
                font-family: inherit;
              ">
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <button style="
                background: transparent;
                border: none;
                color: #7f8c8d;
                cursor: pointer;
                padding: 4px;
                font-size: 16px;
              ">🎤</button>
              <button id="chefai-send-btn" style="
                background: #3498db;
                border: none;
                color: white;
                padding: 6px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
              ">Send</button>
            </div>
          </div>


        </div>

        <!-- Recipe Generation Section -->
        <div style="
          background: #34495e;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 24px;
          border: 1px solid #4a5f7a;
        ">
          <h3 style="
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: #ecf0f1;
          ">🍳 Recipe Generator</h3>

          <p style="
            margin: 0 0 16px 0;
            font-size: 14px;
            color: #bdc3c7;
            line-height: 1.4;
          ">
            Generate personalized recipes using AI. Configure your preferences in settings for better results.
          </p>

          <button id="chefai-get-started" style="
            background: #3498db;
            border: none;
            color: white;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
          ">
            🚀 Start Generating Recipes
          </button>
        </div>
      </div>

      <!-- Overlay Background -->
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);
        z-index: 10000;
      "></div>
    `;

    // Show sidebar with animation
    setTimeout(() => {
      const sidebar = this.state.container.querySelector('.chefai-sidebar');
      if (sidebar) {
        sidebar.style.transform = 'translateX(0)';
      }
    }, 50);

    // Add event listeners
    const closeBtn = this.state.container.querySelector('#chefai-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideInterface());
      closeBtn.addEventListener('mouseenter', () => {
        closeBtn.style.background = 'rgba(255, 255, 255, 0.1)';
        closeBtn.style.color = '#ffffff';
      });
      closeBtn.addEventListener('mouseleave', () => {
        closeBtn.style.background = 'transparent';
        closeBtn.style.color = '#bdc3c7';
      });
    }

    // Dashboard button
    const dashboardBtn = this.state.container.querySelector('#chefai-dashboard-btn');
    if (dashboardBtn) {
      dashboardBtn.addEventListener('click', () => {
        // Open dashboard in new tab
        chrome.runtime.sendMessage({action: 'openDashboard'});
      });
      dashboardBtn.addEventListener('mouseenter', () => {
        dashboardBtn.style.background = '#2980b9';
      });
      dashboardBtn.addEventListener('mouseleave', () => {
        dashboardBtn.style.background = '#3498db';
      });
    }

    const sendBtn = this.state.container.querySelector('#chefai-send-btn');
    if (sendBtn) {
      sendBtn.addEventListener('click', () => {
        const input = this.state.container.querySelector('#chefai-input');
        if (input && input.value.trim()) {
          this.handleRecipeGeneration(input.value.trim());
          input.value = '';
        }
      });
    }

    const input = this.state.container.querySelector('#chefai-input');
    if (input) {
      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const value = input.value.trim();
          if (value) {
            this.handleRecipeGeneration(value);
            input.value = '';
          }
        }
      });
    }

    const getStartedBtn = this.state.container.querySelector('#chefai-get-started');
    if (getStartedBtn) {
      getStartedBtn.addEventListener('click', () => {
        const input = this.state.container.querySelector('#chefai-input');
        if (input) {
          input.focus();
          input.placeholder = 'Enter ingredients you have (e.g., chicken, rice, vegetables)...';
        }
      });
    }

    // Add hover effects for quick action buttons
    const quickActionBtns = this.state.container.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
      btn.addEventListener('mouseenter', () => {
        btn.style.background = '#4a5f7a';
        btn.style.borderColor = '#5a6f8a';
      });
      btn.addEventListener('mouseleave', () => {
        btn.style.background = '#34495e';
        btn.style.borderColor = '#4a5f7a';
      });
    });
  }

  async handleRecipeGeneration(ingredients) {
    try {
      // Show loading state
      const sendBtn = this.state.container.querySelector('#chefai-send-btn');
      if (sendBtn) {
        sendBtn.textContent = 'Generating...';
        sendBtn.disabled = true;
      }

      // Generate recipe using Gemini API
      const recipe = await this.generateRecipeWithAPI(ingredients);
      this.showRecipeResult(recipe);

      // Reset button
      if (sendBtn) {
        sendBtn.textContent = 'Send';
        sendBtn.disabled = false;
      }

    } catch (error) {
      console.error('Error generating recipe:', error);

      // Show error message
      this.showErrorMessage(error.message);

      // Reset button on error
      const sendBtn = this.state.container.querySelector('#chefai-send-btn');
      if (sendBtn) {
        sendBtn.textContent = 'Send';
        sendBtn.disabled = false;
      }
    }
  }

  async generateRecipeWithAPI(ingredientsText) {
    // Initialize Gemini API
    const geminiAPI = new GeminiAPI();
    const initialized = await geminiAPI.initialize();

    if (!initialized) {
      throw new Error('Please configure your Gemini API key in the extension settings.');
    }

    // Parse ingredients from text
    const ingredients = ingredientsText.split(',').map(ing => ing.trim()).filter(ing => ing);

    if (ingredients.length === 0) {
      throw new Error('Please provide at least one ingredient.');
    }

    // Generate recipe
    const recipe = await geminiAPI.generateRecipe(ingredients, {
      cuisine: 'any',
      difficulty: 'medium',
      cookingTime: 60
    });

    return recipe;
  }

  showErrorMessage(message) {
    const errorHTML = `
      <div style="
        background: #e74c3c;
        border-radius: 8px;
        padding: 16px;
        margin-top: 16px;
        border: 1px solid #c0392b;
      ">
        <h4 style="
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
        ">❌ Error</h4>
        <p style="
          margin: 0;
          color: #ffffff;
          font-size: 14px;
          line-height: 1.4;
        ">${message}</p>
      </div>
    `;

    const chatArea = this.state.container.querySelector('#chefai-chat-area');
    if (chatArea) {
      chatArea.innerHTML += errorHTML;
      chatArea.scrollTop = chatArea.scrollHeight;
    }
  }

  showRecipeResult(recipe) {
    // Handle both string ingredients and recipe objects
    let resultHTML;

    if (typeof recipe === 'string') {
      // Fallback for string input (when API is not configured)
      resultHTML = `
        <div style="
          background: #34495e;
          border-radius: 8px;
          padding: 16px;
          margin-top: 16px;
          border: 1px solid #4a5f7a;
        ">
          <h4 style="
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: #ecf0f1;
          ">🍳 Recipe Suggestion</h4>
          <p style="
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #bdc3c7;
            line-height: 1.4;
          ">
            Based on your ingredients: <strong>${recipe}</strong>
          </p>
          <div style="
            background: #2c3e50;
            border-radius: 6px;
            padding: 12px;
            border: 1px solid #34495e;
          ">
            <p style="
              margin: 0;
              font-size: 14px;
              color: #ecf0f1;
              line-height: 1.4;
            ">
              To get personalized recipes, please configure your AI provider in the extension settings.
              Click the ChefAI extension icon and set up your preferred AI service (OpenRouter, Gemini, or OpenAI).
            </p>
          </div>
        </div>
      `;
    } else {
      // Display actual recipe from API
      const ingredientsList = recipe.ingredients.map(ing => `<li style="margin: 4px 0; color: #bdc3c7;">${ing}</li>`).join('');
      const instructionsList = recipe.instructions.map(inst => `<li style="margin: 8px 0; color: #bdc3c7; line-height: 1.4;">${inst}</li>`).join('');

      resultHTML = `
        <div style="
          background: #34495e;
          border-radius: 8px;
          padding: 20px;
          margin-top: 16px;
          border: 1px solid #4a5f7a;
          max-height: 400px;
          overflow-y: auto;
        ">
          <h3 style="
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #ecf0f1;
          ">🍳 ${recipe.title}</h3>

          ${recipe.description ? `
            <p style="
              margin: 0 0 16px 0;
              font-size: 14px;
              color: #bdc3c7;
              line-height: 1.4;
              font-style: italic;
            ">${recipe.description}</p>
          ` : ''}

          <div style="margin-bottom: 16px;">
            <h4 style="
              margin: 0 0 8px 0;
              font-size: 16px;
              font-weight: 600;
              color: #3498db;
            ">📋 Ingredients</h4>
            <ul style="
              margin: 0;
              padding-left: 20px;
              list-style-type: disc;
            ">
              ${ingredientsList}
            </ul>
          </div>

          <div style="margin-bottom: 16px;">
            <h4 style="
              margin: 0 0 8px 0;
              font-size: 16px;
              font-weight: 600;
              color: #e67e22;
            ">👨‍🍳 Instructions</h4>
            <ol style="
              margin: 0;
              padding-left: 20px;
            ">
              ${instructionsList}
            </ol>
          </div>

          ${recipe.cookingInfo && Object.keys(recipe.cookingInfo).length > 0 ? `
            <div style="
              background: #2c3e50;
              border-radius: 6px;
              padding: 12px;
              border: 1px solid #34495e;
            ">
              <h4 style="
                margin: 0 0 8px 0;
                font-size: 14px;
                font-weight: 600;
                color: #27ae60;
              ">ℹ️ Cooking Information</h4>
              ${Object.entries(recipe.cookingInfo).map(([key, value]) => `
                <span style="
                  display: inline-block;
                  margin: 2px 8px 2px 0;
                  font-size: 12px;
                  color: #95a5a6;
                ">${key.replace('_', ' ')}: ${value}</span>
              `).join('')}
            </div>
          ` : ''}
        </div>
      `;
    }

    // Find the main content area and add the result
    const mainContent = this.state.container.querySelector('[style*="padding: 24px 20px"]');
    if (mainContent) {
      // Remove any existing results
      const existingResults = mainContent.querySelectorAll('[style*="margin-top: 16px"]');
      existingResults.forEach(result => result.remove());

      // Add new result
      mainContent.insertAdjacentHTML('beforeend', resultHTML);

      // Scroll to show the result
      const newResult = mainContent.lastElementChild;
      if (newResult) {
        newResult.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  }

  // Apply theme updates in real-time
  applyThemeUpdates(data) {
    if (window.customizationManager) {
      // Theme will be applied automatically by customization manager
      console.log('Theme updated:', data.currentTheme);

      // Show notification
      if (window.notificationManager) {
        window.notificationManager.show({
          type: 'info',
          title: 'Theme Updated',
          message: `Switched to ${data.currentTheme} theme`,
          duration: 3000
        });
      }
    }
  }

  // Apply settings updates in real-time
  applySettingsUpdates(data) {
    console.log('Settings updated:', data);

    // Refresh interface if needed
    if (this.state.isVisible) {
      this.refreshInterface();
    }
  }

  // Handle API key changes
  handleAPIKeyChange(data) {
    console.log('API key changed');

    // Show notification
    if (window.notificationManager) {
      window.notificationManager.show({
        type: 'success',
        title: 'API Key Updated',
        message: 'Your API configuration has been updated',
        duration: 4000
      });
    }
  }

  // Refresh interface with current settings
  refreshInterface() {
    if (!this.state.container) return;

    // Re-apply current theme
    if (window.customizationManager) {
      const currentTheme = window.customizationManager.getCurrentTheme();
      if (currentTheme) {
        this.applyThemeToInterface(currentTheme);
      }
    }
  }

  // Apply theme to interface elements
  applyThemeToInterface(theme) {
    if (!this.state.container) return;

    // Update CSS variables for the interface
    const interfaceElements = this.state.container.querySelectorAll('*');
    interfaceElements.forEach(element => {
      // Apply theme colors to elements that use CSS variables
      if (element.style.background && element.style.background.includes('var(')) {
        element.style.setProperty('background', `var(--chefai-color-surface, ${theme.colors.surface})`);
      }
      if (element.style.color && element.style.color.includes('var(')) {
        element.style.setProperty('color', `var(--chefai-color-text, ${theme.colors.text})`);
      }
    });
  }

  // Public methods for external access
  getStatus() {
    return {
      isVisible: this.state.isVisible,
      isInitialized: this.state.isInitialized,
      currentMode: this.state.currentMode,
      services: {
        realtimeManager: !!window.realtimeManager,
        customizationManager: !!window.customizationManager,
        notificationManager: !!window.notificationManager
      }
    };
  }

  destroy() {
    try {
      this.hideInterface();
      
      if (this.state.floatingButton) {
        this.state.floatingButton.remove();
        this.state.floatingButton = null;
      }

      document.body.classList.remove('chefai-interface-open');
      
      this.state.isInitialized = false;
    } catch (error) {
      console.error('Failed to destroy ChefAI injector:', error);
    }
  }
}

// Initialize the injector
let chefAIInjector = null;

// Only initialize if we're in a valid context
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  chefAIInjector = new EnhancedChefAIInjector();
}

// Export for potential external access
if (typeof window !== 'undefined') {
  window.ChefAIInjector = chefAIInjector;
}
