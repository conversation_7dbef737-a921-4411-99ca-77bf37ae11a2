// Advanced Settings Service - Manages user preferences and advanced configurations
export interface AdvancedSettings {
  // Target Audience Settings
  targetAudience: 'family' | 'adults' | 'children' | 'elderly' | 'teens' | 'couples' | 'single';
  
  // Recipe Preferences
  recipeComplexity: 'simple' | 'medium' | 'complex' | 'gourmet';
  recipeStyle: 'traditional' | 'modern' | 'comfort' | 'healthy' | 'gourmet' | 'quick';
  spiceLevel: 'mild' | 'medium' | 'hot' | 'very-hot' | 'variable';
  
  // Cultural and Dietary Preferences
  culturalPreferences: string[];
  dietaryProfile: string[];
  
  // User Profile
  cookingSkillLevel: 'beginner' | 'intermediate' | 'advanced' | 'professional';
  healthGoals: string[];
  nutritionFocus: string[];
  
  // Kitchen and Cooking Preferences
  preferredCookingMethods: string[];
  kitchenEquipment: string[];
  
  // Time and Budget Constraints
  timeConstraints: 'quick' | 'moderate' | 'flexible' | 'weekend';
  budgetRange: 'budget' | 'medium' | 'premium' | 'luxury';
  
  // Additional Preferences
  seasonalPreferences: boolean;
  mealPlanningPreferences: string[];
  
  // AI Behavior Settings
  creativityLevel: number; // 0-100
  detailLevel: 'basic' | 'detailed' | 'comprehensive';
  includeNutritionInfo: boolean;
  includeCookingTips: boolean;
  includeSubstitutions: boolean;
  includeVariations: boolean;
  
  // Language and Localization
  preferredLanguage: string;
  measurementSystem: 'metric' | 'imperial' | 'mixed';
  
  // Recipe Generation Preferences
  defaultServings: number;
  maxIngredients: number;
  minIngredients: number;
  preferCommonIngredients: boolean;
  avoidRareIngredients: boolean;
}

export interface RecipeGenerationContext {
  settings: AdvancedSettings;
  userHistory: string[];
  seasonalContext: string;
  availableIngredients?: string[];
}

export class AdvancedSettingsService {
  private static instance: AdvancedSettingsService;
  private settings: AdvancedSettings | null = null;
  private readonly STORAGE_KEY = 'chefai_advanced_settings';

  private constructor() {
    this.loadSettings();
  }

  public static getInstance(): AdvancedSettingsService {
    if (!AdvancedSettingsService.instance) {
      AdvancedSettingsService.instance = new AdvancedSettingsService();
    }
    return AdvancedSettingsService.instance;
  }

  private getDefaultSettings(): AdvancedSettings {
    return {
      // Target Audience
      targetAudience: 'family',
      
      // Recipe Preferences
      recipeComplexity: 'medium',
      recipeStyle: 'traditional',
      spiceLevel: 'medium',
      
      // Cultural and Dietary
      culturalPreferences: [],
      dietaryProfile: [],
      
      // User Profile
      cookingSkillLevel: 'intermediate',
      healthGoals: [],
      nutritionFocus: [],
      
      // Kitchen and Cooking
      preferredCookingMethods: [],
      kitchenEquipment: ['Oven', 'Stovetop'],
      
      // Time and Budget
      timeConstraints: 'flexible',
      budgetRange: 'medium',
      
      // Additional Preferences
      seasonalPreferences: true,
      mealPlanningPreferences: [],
      
      // AI Behavior
      creativityLevel: 70,
      detailLevel: 'detailed',
      includeNutritionInfo: true,
      includeCookingTips: true,
      includeSubstitutions: true,
      includeVariations: false,
      
      // Language and Localization
      preferredLanguage: 'en',
      measurementSystem: 'metric',
      
      // Recipe Generation
      defaultServings: 4,
      maxIngredients: 15,
      minIngredients: 5,
      preferCommonIngredients: true,
      avoidRareIngredients: false
    };
  }

  public async loadSettings(): Promise<AdvancedSettings> {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEY]);
      if (result[this.STORAGE_KEY]) {
        this.settings = { ...this.getDefaultSettings(), ...result[this.STORAGE_KEY] };
      } else {
        this.settings = this.getDefaultSettings();
        await this.saveSettings();
      }
      return this.settings;
    } catch (error) {
      console.error('Failed to load advanced settings:', error);
      this.settings = this.getDefaultSettings();
      return this.settings;
    }
  }

  public async saveSettings(newSettings?: Partial<AdvancedSettings>): Promise<void> {
    if (newSettings) {
      this.settings = { ...this.settings, ...newSettings } as AdvancedSettings;
    }
    
    try {
      await chrome.storage.local.set({
        [this.STORAGE_KEY]: this.settings
      });
    } catch (error) {
      console.error('Failed to save advanced settings:', error);
      throw error;
    }
  }

  public getSettings(): AdvancedSettings {
    return this.settings || this.getDefaultSettings();
  }

  public async updateSetting<K extends keyof AdvancedSettings>(
    key: K, 
    value: AdvancedSettings[K]
  ): Promise<void> {
    const currentSettings = this.getSettings();
    await this.saveSettings({ [key]: value } as Partial<AdvancedSettings>);
  }

  public generateSystemPrompt(): string {
    const settings = this.getSettings();
    
    let prompt = `You are ChefAI, an expert culinary assistant specialized in creating personalized recipes. `;
    
    // Target Audience Context
    switch (settings.targetAudience) {
      case 'family':
        prompt += `Create family-friendly recipes suitable for all ages. `;
        break;
      case 'children':
        prompt += `Focus on kid-friendly recipes with simple flavors and fun presentation. `;
        break;
      case 'elderly':
        prompt += `Create recipes that are easy to prepare and gentle on digestion. `;
        break;
      case 'adults':
        prompt += `Create sophisticated recipes for adult palates. `;
        break;
    }

    // Skill Level Context
    switch (settings.cookingSkillLevel) {
      case 'beginner':
        prompt += `Provide simple, step-by-step instructions with basic techniques. `;
        break;
      case 'advanced':
        prompt += `Include advanced cooking techniques and professional tips. `;
        break;
      case 'professional':
        prompt += `Use professional culinary terminology and advanced techniques. `;
        break;
    }

    // Complexity Preference
    switch (settings.recipeComplexity) {
      case 'simple':
        prompt += `Keep recipes simple with 5-10 common ingredients. `;
        break;
      case 'gourmet':
        prompt += `Create sophisticated gourmet recipes with premium ingredients. `;
        break;
    }

    // Cultural Preferences
    if (settings.culturalPreferences.length > 0) {
      prompt += `Prefer ${settings.culturalPreferences.join(', ')} culinary traditions. `;
    }

    // Dietary Restrictions
    if (settings.dietaryProfile.length > 0) {
      prompt += `Ensure recipes are ${settings.dietaryProfile.join(', ')} compliant. `;
    }

    // Health Goals
    if (settings.healthGoals.length > 0) {
      prompt += `Focus on ${settings.healthGoals.join(', ')} health benefits. `;
    }

    // Time Constraints
    switch (settings.timeConstraints) {
      case 'quick':
        prompt += `Prioritize recipes that can be completed in under 30 minutes. `;
        break;
      case 'weekend':
        prompt += `Include elaborate recipes suitable for weekend cooking projects. `;
        break;
    }

    // Equipment Constraints
    if (settings.kitchenEquipment.length > 0) {
      prompt += `Only suggest recipes that can be made with: ${settings.kitchenEquipment.join(', ')}. `;
    }

    // Seasonal Preferences
    if (settings.seasonalPreferences) {
      const currentSeason = this.getCurrentSeason();
      prompt += `Prefer ${currentSeason} seasonal ingredients when possible. `;
    }

    // Detail Level
    switch (settings.detailLevel) {
      case 'comprehensive':
        prompt += `Provide comprehensive details including cooking science, variations, and troubleshooting tips. `;
        break;
      case 'basic':
        prompt += `Keep instructions concise and straightforward. `;
        break;
    }

    // Additional Features
    const features = [];
    if (settings.includeNutritionInfo) features.push('detailed nutritional information');
    if (settings.includeCookingTips) features.push('professional cooking tips');
    if (settings.includeSubstitutions) features.push('ingredient substitutions');
    if (settings.includeVariations) features.push('recipe variations');
    
    if (features.length > 0) {
      prompt += `Always include: ${features.join(', ')}. `;
    }

    prompt += `Generate recipes that are practical, delicious, and perfectly suited to the user's preferences and constraints.`;

    return prompt;
  }

  private getCurrentSeason(): string {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'fall';
    return 'winter';
  }

  public getRecipeGenerationContext(): RecipeGenerationContext {
    return {
      settings: this.getSettings(),
      userHistory: [], // This could be populated from recipe history
      seasonalContext: this.getCurrentSeason()
    };
  }

  public async resetToDefaults(): Promise<void> {
    this.settings = this.getDefaultSettings();
    await this.saveSettings();
  }

  public exportSettings(): string {
    return JSON.stringify(this.getSettings(), null, 2);
  }

  public async importSettings(settingsJson: string): Promise<boolean> {
    try {
      const importedSettings = JSON.parse(settingsJson);
      // Validate the imported settings structure
      const validatedSettings = { ...this.getDefaultSettings(), ...importedSettings };
      await this.saveSettings(validatedSettings);
      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }
}
