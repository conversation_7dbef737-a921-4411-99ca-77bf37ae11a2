# 🚀 ChefAI Quick Start Guide

## Installation & Setup

### 1. Install the Extension
1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" (top right toggle)
4. Click "Load unpacked" and select the project folder
5. The ChefAI icon 🍳 should appear in your extensions

### 2. First Time Setup
1. Click the ChefAI extension icon in the toolbar
2. Click "📊 Open Dashboard" 
3. Go to "LLM Settings" tab
4. Configure your AI provider:

#### Option A: OpenRouter (Recommended)
- Visit [OpenRouter.ai](https://openrouter.ai/)
- Sign up and get your API key
- Select "OpenRouter" as provider
- Enter your API key
- Choose a model (GPT-4 recommended)

#### Option B: Gemini
- Visit [Google AI Studio](https://makersuite.google.com/)
- Get your Gemini API key
- Select "Gemini" as provider
- Enter your API key

#### Option C: OpenAI
- Visit [OpenAI Platform](https://platform.openai.com/)
- Get your API key
- Select "OpenAI" as provider
- Enter your API key

### 3. Test Your Setup
1. Click "Test Connection" in the LLM Settings
2. If successful, you'll see "✅ Connection successful!"
3. If failed, check your API key and try again

## How to Use

### Quick Recipe Generation
1. **Method 1**: Click the floating 🍳 button on any webpage
2. **Method 2**: Press `Ctrl+Shift+C` on any webpage
3. **Method 3**: Click the extension icon → "📋 Quick Sidebar"

### Advanced Dashboard
1. Click the extension icon → "📊 Open Dashboard"
2. Explore different tabs:
   - **Recipe Generator**: Main interface
   - **LLM Settings**: AI configuration
   - **Advanced Settings**: Detailed preferences
   - **Customization**: UI personalization

### Customize Your Experience
1. Open Dashboard → "Advanced Settings"
2. Set your preferences:
   - Target audience (family, adults, children, etc.)
   - Recipe complexity (simple to gourmet)
   - Cultural preferences (Mediterranean, Asian, etc.)
   - Dietary restrictions (vegetarian, keto, etc.)
   - Cooking skill level
   - Available kitchen equipment
   - Time constraints
   - Budget range

## Features Overview

### 🤖 AI Models Supported
- **OpenRouter**: Access to multiple models (GPT-4, Claude, etc.)
- **Gemini**: Google's advanced AI model
- **OpenAI**: GPT-3.5, GPT-4, and other models
- **Anthropic**: Claude models (via OpenRouter)

### 🎯 Smart Personalization
- **Target Audience**: Recipes for families, kids, adults, seniors
- **Skill Level**: From beginner to professional chef
- **Dietary Needs**: Vegetarian, vegan, keto, gluten-free, etc.
- **Cultural Cuisine**: Mediterranean, Asian, Latin, European, etc.
- **Equipment Based**: Recipes based on your kitchen tools
- **Time & Budget**: Flexible constraints for busy lifestyles

### 🎨 Beautiful Interface
- **Glassmorphism Design**: Modern translucent interface
- **Right Sidebar**: Non-intrusive sliding panel
- **Responsive**: Works on all screen sizes
- **Customizable**: Themes, colors, and layout options

### ⚡ Advanced Features
- **Real-time Updates**: Live synchronization across tabs
- **Recipe Storage**: Save and organize your recipes
- **Performance Optimized**: Fast loading and smooth animations
- **Keyboard Shortcuts**: Quick access with hotkeys

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+Shift+C` | Toggle ChefAI interface |
| `Ctrl+Enter` | Generate recipe (when in generator) |
| `Ctrl+S` | Save current recipe |
| `Escape` | Close interface |

## Troubleshooting

### Common Issues

**❌ "API Key Not Working"**
- Verify your API key is correct
- Check if you have sufficient credits
- Ensure the provider is selected correctly

**❌ "Interface Not Loading"**
- Refresh the webpage and try again
- Check if the extension is enabled
- Try disabling other extensions temporarily

**❌ "Slow Performance"**
- Clear cache in Customization settings
- Reduce animation settings
- Check your internet connection

**❌ "Recipe Not Saving"**
- Check browser storage permissions
- Try generating a new recipe
- Clear browser data and reconfigure

### Getting Help

1. **Check Status**: Extension popup shows system status
2. **Debug Mode**: Enable in Advanced Settings → Behavior
3. **Reset Settings**: Use "Reset to Defaults" in Customization
4. **Contact Support**: Open GitHub issues for bugs

## Tips for Best Results

### Recipe Generation
1. **Be Specific**: Include specific ingredients you have
2. **Set Preferences**: Configure your dietary needs first
3. **Use Context**: Mention occasion (dinner party, quick lunch, etc.)
4. **Specify Constraints**: Time limits, equipment, skill level

### Customization
1. **Create Profiles**: Set up different profiles for different needs
2. **Use Shortcuts**: Learn keyboard shortcuts for efficiency
3. **Optimize Performance**: Adjust settings based on your device
4. **Regular Updates**: Keep settings updated as preferences change

## Advanced Usage

### Multiple Profiles
1. Go to Customization → Create Profile
2. Set up different configurations:
   - "Family Dinners" profile
   - "Quick Lunches" profile
   - "Weekend Cooking" profile
3. Switch between profiles as needed

### API Optimization
1. **Model Selection**: Choose appropriate model for your needs
2. **Temperature Settings**: Adjust creativity vs consistency
3. **Token Limits**: Set appropriate limits for cost control
4. **Batch Requests**: Use efficiently to save costs

### Integration Tips
1. **Bookmark Recipes**: Save favorites for quick access
2. **Export Data**: Backup your recipes and settings
3. **Share Settings**: Export/import configurations
4. **Performance Monitoring**: Check metrics in dashboard

---

**🎉 You're all set! Start generating amazing recipes with ChefAI!**

For more detailed information, check the full [README.md](README.md) file.
