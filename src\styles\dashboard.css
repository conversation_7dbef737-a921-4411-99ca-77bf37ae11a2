/* Dashboard Interface Styles */
.dashboard-interface {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.dashboard-interface.visible {
  opacity: 1;
  visibility: visible;
}

.dashboard-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
}

.dashboard-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 95%;
  max-width: 1400px;
  height: 90%;
  max-height: 900px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  color: white;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-info {
  flex: 1;
}

.dashboard-title {
  margin: 0 0 8px 0;
  font-size: 2.2em;
  font-weight: 700;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-subtitle {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
  font-weight: 400;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 25px;
}

.status-indicators {
  display: flex;
  gap: 15px;
}

.status-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.status-indicator.active {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.5);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

.status-indicator.inactive {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.5);
}

.status-icon {
  font-size: 1.2em;
}

.status-text {
  font-size: 0.8em;
  font-weight: 500;
}

.dashboard-close {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: white;
  font-size: 1.8em;
  cursor: pointer;
  padding: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
}

.dashboard-close:hover {
  background: rgba(255, 100, 100, 0.3);
  border-color: rgba(255, 100, 100, 0.5);
  transform: rotate(90deg) scale(1.05);
}

/* Dashboard Navigation */
.dashboard-navigation {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 25px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.nav-tab:last-child {
  border-right: none;
}

.nav-tab::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-tab.active {
  color: white;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.1));
}

.nav-tab.active::before {
  transform: scaleX(1);
}

.nav-tab:hover:not(.active) {
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.08);
}

.tab-icon {
  font-size: 1.6em;
  min-width: 35px;
  text-align: center;
}

.tab-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tab-name {
  font-size: 1.1em;
  font-weight: 600;
}

.tab-description {
  font-size: 0.85em;
  opacity: 0.8;
  line-height: 1.3;
}

/* Dashboard Main Content */
.dashboard-main {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.dashboard-section {
  height: 100%;
  overflow-y: auto;
  padding: 0;
}

.dashboard-section .sidebar-interface {
  position: relative;
  width: 100%;
  height: 100%;
  opacity: 1;
  visibility: visible;
}

.dashboard-section .sidebar-content {
  position: relative;
  transform: none;
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.dashboard-section .sidebar-overlay {
  display: none;
}

/* Dashboard Footer */
.dashboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-value {
  font-size: 1.2em;
  font-weight: 600;
  color: #FFD700;
}

.stat-label {
  font-size: 0.85em;
  opacity: 0.8;
}

.footer-actions {
  display: flex;
  gap: 15px;
}

.footer-button {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9em;
}

.footer-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.footer-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-content {
    width: 98%;
    height: 95%;
  }
  
  .dashboard-navigation {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .nav-tab {
    padding: 15px 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
  
  .dashboard-header {
    padding: 20px 25px;
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .header-status {
    width: 100%;
    justify-content: space-between;
  }
  
  .dashboard-title {
    font-size: 1.8em;
  }
  
  .dashboard-navigation {
    grid-template-columns: 1fr;
  }
  
  .nav-tab {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .nav-tab:last-child {
    border-bottom: none;
  }
  
  .dashboard-footer {
    padding: 15px 25px;
    flex-direction: column;
    gap: 15px;
  }
  
  .footer-stats {
    gap: 20px;
  }
  
  .footer-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Scrollbar Styling for Dashboard */
.dashboard-section::-webkit-scrollbar {
  width: 8px;
}

.dashboard-section::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.dashboard-section::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-section::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.4));
}

/* Animation for dashboard appearance */
@keyframes dashboardSlideIn {
  from {
    transform: translate(-50%, -50%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.dashboard-content {
  animation: dashboardSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
