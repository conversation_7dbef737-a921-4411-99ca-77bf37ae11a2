// Performance Optimizer - Enhances ChefAI extension performance
export interface PerformanceMetrics {
  memoryUsage: number;
  loadTime: number;
  renderTime: number;
  apiResponseTime: number;
  cacheHitRate: number;
  errorRate: number;
  userInteractionDelay: number;
}

export interface OptimizationConfig {
  enableLazyLoading: boolean;
  enableCaching: boolean;
  enableCompression: boolean;
  enablePreloading: boolean;
  maxCacheSize: number;
  cacheExpiration: number;
  debounceDelay: number;
  throttleDelay: number;
}

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private metrics: PerformanceMetrics;
  private config: OptimizationConfig;
  private cache: Map<string, { data: any; timestamp: number; size: number }> = new Map();
  private currentCacheSize: number = 0;
  private performanceObserver: PerformanceObserver | null = null;
  private memoryMonitorInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.metrics = {
      memoryUsage: 0,
      loadTime: 0,
      renderTime: 0,
      apiResponseTime: 0,
      cacheHitRate: 0,
      errorRate: 0,
      userInteractionDelay: 0
    };

    this.config = {
      enableLazyLoading: true,
      enableCaching: true,
      enableCompression: true,
      enablePreloading: true,
      maxCacheSize: 50 * 1024 * 1024, // 50MB
      cacheExpiration: 30 * 60 * 1000, // 30 minutes
      debounceDelay: 300,
      throttleDelay: 100
    };

    this.initializePerformanceMonitoring();
  }

  public static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  private initializePerformanceMonitoring(): void {
    // Monitor performance entries
    if (typeof PerformanceObserver !== 'undefined') {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry);
        }
      });

      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'navigation', 'resource', 'paint'] 
      });
    }

    // Monitor memory usage
    this.startMemoryMonitoring();

    // Clean up cache periodically
    setInterval(() => this.cleanupCache(), 5 * 60 * 1000); // Every 5 minutes
  }

  private processPerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        this.metrics.loadTime = navEntry.loadEventEnd - navEntry.navigationStart;
        break;
      
      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          this.metrics.renderTime = entry.startTime;
        }
        break;
      
      case 'measure':
        if (entry.name.startsWith('chefai-api-')) {
          this.metrics.apiResponseTime = entry.duration;
        }
        break;
    }
  }

  private startMemoryMonitoring(): void {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      this.memoryMonitorInterval = setInterval(() => {
        const memory = (performance as any).memory;
        if (memory) {
          this.metrics.memoryUsage = memory.usedJSHeapSize;
          
          // Trigger garbage collection if memory usage is high
          if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
            this.triggerMemoryCleanup();
          }
        }
      }, 10000); // Every 10 seconds
    }
  }

  private triggerMemoryCleanup(): void {
    // Clear old cache entries
    this.cleanupCache();
    
    // Clear old performance entries
    if (typeof performance !== 'undefined' && performance.clearMeasures) {
      performance.clearMeasures();
    }
    
    // Emit memory cleanup event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('chefai-memory-cleanup'));
    }
  }

  // Caching utilities
  public setCache(key: string, data: any, customExpiration?: number): void {
    if (!this.config.enableCaching) return;

    const serialized = JSON.stringify(data);
    const size = new Blob([serialized]).size;
    
    // Check if adding this item would exceed cache size limit
    if (this.currentCacheSize + size > this.config.maxCacheSize) {
      this.evictLeastRecentlyUsed(size);
    }

    const expiration = customExpiration || this.config.cacheExpiration;
    this.cache.set(key, {
      data,
      timestamp: Date.now() + expiration,
      size
    });
    
    this.currentCacheSize += size;
  }

  public getCache(key: string): any | null {
    if (!this.config.enableCaching) return null;

    const cached = this.cache.get(key);
    if (!cached) return null;

    // Check if expired
    if (Date.now() > cached.timestamp) {
      this.cache.delete(key);
      this.currentCacheSize -= cached.size;
      return null;
    }

    // Update cache hit rate
    this.updateCacheHitRate(true);
    return cached.data;
  }

  private evictLeastRecentlyUsed(requiredSize: number): void {
    const entries = Array.from(this.cache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);

    let freedSize = 0;
    for (const [key, value] of entries) {
      this.cache.delete(key);
      this.currentCacheSize -= value.size;
      freedSize += value.size;
      
      if (freedSize >= requiredSize) break;
    }
  }

  private updateCacheHitRate(hit: boolean): void {
    // Simple moving average for cache hit rate
    const weight = 0.1;
    this.metrics.cacheHitRate = this.metrics.cacheHitRate * (1 - weight) + (hit ? 1 : 0) * weight;
  }

  private cleanupCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, value] of this.cache.entries()) {
      if (now > value.timestamp) {
        expiredKeys.push(key);
        this.currentCacheSize -= value.size;
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  // Performance utilities
  public debounce<T extends (...args: any[]) => any>(
    func: T,
    delay?: number
  ): (...args: Parameters<T>) => void {
    const actualDelay = delay || this.config.debounceDelay;
    let timeoutId: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), actualDelay);
    };
  }

  public throttle<T extends (...args: any[]) => any>(
    func: T,
    delay?: number
  ): (...args: Parameters<T>) => void {
    const actualDelay = delay || this.config.throttleDelay;
    let lastCall = 0;

    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= actualDelay) {
        lastCall = now;
        func.apply(this, args);
      }
    };
  }

  public measurePerformance<T>(
    name: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const startMark = `${name}-start`;
    const endMark = `${name}-end`;
    const measureName = `chefai-${name}`;

    performance.mark(startMark);

    return operation().then(
      (result) => {
        performance.mark(endMark);
        performance.measure(measureName, startMark, endMark);
        return result;
      },
      (error) => {
        performance.mark(endMark);
        performance.measure(measureName, startMark, endMark);
        throw error;
      }
    );
  }

  // Lazy loading utilities
  public createLazyComponent<T>(
    loader: () => Promise<T>,
    fallback?: React.ComponentType
  ): React.ComponentType {
    if (!this.config.enableLazyLoading) {
      // Return a component that loads immediately
      return React.lazy(loader) as any;
    }

    return React.lazy(loader) as any;
  }

  public preloadResource(url: string): Promise<void> {
    if (!this.config.enablePreloading) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to preload ${url}`));
      document.head.appendChild(link);
    });
  }

  // Image optimization
  public optimizeImage(
    imageUrl: string,
    maxWidth: number = 800,
    quality: number = 0.8
  ): Promise<string> {
    return new Promise((resolve) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        
        // Calculate new dimensions
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;
        
        // Draw and compress
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        const optimizedUrl = canvas.toDataURL('image/jpeg', quality);
        
        resolve(optimizedUrl);
      };
      
      img.onerror = () => resolve(imageUrl); // Fallback to original
      img.src = imageUrl;
    });
  }

  // Bundle optimization
  public async loadChunk(chunkName: string): Promise<any> {
    const cacheKey = `chunk-${chunkName}`;
    const cached = this.getCache(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const module = await import(/* webpackChunkName: "[request]" */ `../chunks/${chunkName}`);
      this.setCache(cacheKey, module);
      return module;
    } catch (error) {
      console.error(`Failed to load chunk ${chunkName}:`, error);
      throw error;
    }
  }

  // Configuration
  public updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getConfig(): OptimizationConfig {
    return { ...this.config };
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Resource cleanup
  public cleanup(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }

    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
      this.memoryMonitorInterval = null;
    }

    this.cache.clear();
    this.currentCacheSize = 0;
  }

  // Performance reporting
  public generatePerformanceReport(): {
    metrics: PerformanceMetrics;
    cacheStats: {
      size: number;
      entries: number;
      hitRate: number;
    };
    recommendations: string[];
  } {
    const recommendations: string[] = [];

    // Analyze metrics and provide recommendations
    if (this.metrics.loadTime > 3000) {
      recommendations.push('Consider enabling preloading for critical resources');
    }

    if (this.metrics.memoryUsage > 100 * 1024 * 1024) { // 100MB
      recommendations.push('Memory usage is high, consider reducing cache size');
    }

    if (this.metrics.cacheHitRate < 0.5) {
      recommendations.push('Cache hit rate is low, review caching strategy');
    }

    if (this.metrics.apiResponseTime > 2000) {
      recommendations.push('API response time is slow, consider request optimization');
    }

    return {
      metrics: this.getMetrics(),
      cacheStats: {
        size: this.currentCacheSize,
        entries: this.cache.size,
        hitRate: this.metrics.cacheHitRate
      },
      recommendations
    };
  }
}
