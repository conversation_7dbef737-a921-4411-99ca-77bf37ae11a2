// Background script for ChefAI Chrome Extension

import { Recipe, UserPreferences, ExtensionState } from '../types';

class BackgroundService {
  private static instance: BackgroundService;
  private extensionState: ExtensionState = {
    isInitialized: false,
    recentRecipes: [],
    savedRecipes: [],
    searchHistory: [],
    connectedDevices: [],
    activeTimers: []
  };

  private constructor() {
    this.initialize();
  }

  public static getInstance(): BackgroundService {
    if (!BackgroundService.instance) {
      BackgroundService.instance = new BackgroundService();
    }
    return BackgroundService.instance;
  }

  private async initialize(): Promise<void> {
    try {
      // Load extension state from storage
      await this.loadExtensionState();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Initialize context menus
      this.setupContextMenus();
      
      // Set up alarms for timers
      this.setupAlarms();
      
      this.extensionState.isInitialized = true;
      console.log('ChefAI Background Service initialized');
    } catch (error) {
      console.error('Failed to initialize background service:', error);
    }
  }

  private async loadExtensionState(): Promise<void> {
    try {
      const result = await chrome.storage.local.get([
        'recentRecipes',
        'savedRecipes',
        'searchHistory',
        'connectedDevices',
        'activeTimers'
      ]);

      this.extensionState = {
        ...this.extensionState,
        recentRecipes: result.recentRecipes || [],
        savedRecipes: result.savedRecipes || [],
        searchHistory: result.searchHistory || [],
        connectedDevices: result.connectedDevices || [],
        activeTimers: result.activeTimers || []
      };
    } catch (error) {
      console.error('Failed to load extension state:', error);
    }
  }

  private setupEventListeners(): void {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Handle messages from popup/content scripts
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Handle storage changes
    chrome.storage.onChanged.addListener((changes, areaName) => {
      this.handleStorageChange(changes, areaName);
    });

    // Handle tab updates for recipe detection
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    // Handle alarm events for timers
    chrome.alarms.onAlarm.addListener((alarm) => {
      this.handleAlarm(alarm);
    });
  }

  private setupContextMenus(): void {
    chrome.contextMenus.removeAll(() => {
      // Add context menu for generating recipes from selected text
      chrome.contextMenus.create({
        id: 'generate-recipe-from-selection',
        title: 'Generate Recipe with "%s"',
        contexts: ['selection'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
      });

      // Add context menu for recipe search
      chrome.contextMenus.create({
        id: 'search-recipes',
        title: 'Search Recipes',
        contexts: ['page']
      });

      // Handle context menu clicks
      chrome.contextMenus.onClicked.addListener((info, tab) => {
        this.handleContextMenuClick(info, tab);
      });
    });
  }

  private setupAlarms(): void {
    // Set up periodic alarms for trend analysis
    chrome.alarms.create('trend-analysis', {
      delayInMinutes: 1,
      periodInMinutes: 60 // Run every hour
    });

    // Set up daily cleanup alarm
    chrome.alarms.create('daily-cleanup', {
      delayInMinutes: 1,
      periodInMinutes: 24 * 60 // Run daily
    });
  }

  private handleInstallation(details: chrome.runtime.InstalledDetails): void {
    if (details.reason === 'install') {
      // First time installation
      this.showWelcomeNotification();
      this.openWelcomePage();
    } else if (details.reason === 'update') {
      // Extension updated
      this.showUpdateNotification();
    }
  }

  private async handleMessage(
    message: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): Promise<void> {
    try {
      switch (message.type) {
        case 'GET_EXTENSION_STATE':
          sendResponse({ success: true, data: this.extensionState });
          break;

        case 'GENERATE_RECIPE':
          const recipe = await this.generateRecipe(message.data);
          sendResponse({ success: true, data: recipe });
          break;

        case 'SAVE_RECIPE':
          await this.saveRecipe(message.data);
          sendResponse({ success: true });
          break;

        case 'START_TIMER':
          await this.startTimer(message.data);
          sendResponse({ success: true });
          break;

        case 'STOP_TIMER':
          await this.stopTimer(message.data.timerId);
          sendResponse({ success: true });
          break;

        case 'SEARCH_RECIPES':
          const searchResults = await this.searchRecipes(message.data.query);
          sendResponse({ success: true, data: searchResults });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  private handleStorageChange(
    changes: { [key: string]: chrome.storage.StorageChange },
    areaName: string
  ): void {
    // Update local state when storage changes
    if (areaName === 'local') {
      if (changes.recentRecipes) {
        this.extensionState.recentRecipes = changes.recentRecipes.newValue || [];
      }
      if (changes.savedRecipes) {
        this.extensionState.savedRecipes = changes.savedRecipes.newValue || [];
      }
    }
  }

  private handleTabUpdate(
    tabId: number,
    changeInfo: chrome.tabs.TabChangeInfo,
    tab: chrome.tabs.Tab
  ): void {
    // Detect recipe websites and offer to extract recipes
    if (changeInfo.status === 'complete' && tab.url) {
      this.detectRecipeWebsite(tab);
    }
  }

  private handleAlarm(alarm: chrome.alarms.Alarm): void {
    switch (alarm.name) {
      case 'trend-analysis':
        this.performTrendAnalysis();
        break;
      case 'daily-cleanup':
        this.performDailyCleanup();
        break;
      default:
        // Handle timer alarms
        if (alarm.name.startsWith('timer-')) {
          this.handleTimerAlarm(alarm.name);
        }
    }
  }

  private handleContextMenuClick(
    info: chrome.contextMenus.OnClickData,
    tab?: chrome.tabs.Tab
  ): void {
    switch (info.menuItemId) {
      case 'generate-recipe-from-selection':
        if (info.selectionText) {
          this.generateRecipeFromSelection(info.selectionText);
        }
        break;
      case 'search-recipes':
        this.openRecipeSearch();
        break;
    }
  }

  private showWelcomeNotification(): void {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Welcome to ChefAI!',
      message: 'Your AI-powered recipe generator is ready to use. Click the extension icon to get started!'
    });
  }

  private showUpdateNotification(): void {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'ChefAI Updated!',
      message: 'New features and improvements are now available.'
    });
  }

  private openWelcomePage(): void {
    chrome.tabs.create({
      url: chrome.runtime.getURL('options.html#welcome')
    });
  }

  private async generateRecipe(request: any): Promise<Recipe | null> {
    // This would integrate with the AI service
    // For now, return a mock response
    return null;
  }

  private async saveRecipe(recipe: Recipe): Promise<void> {
    const savedRecipes = [...this.extensionState.savedRecipes, recipe];
    this.extensionState.savedRecipes = savedRecipes;
    await chrome.storage.local.set({ savedRecipes });
  }

  private async startTimer(timerData: any): Promise<void> {
    const timer = {
      id: `timer-${Date.now()}`,
      name: timerData.name,
      duration: timerData.duration,
      remainingTime: timerData.duration,
      isActive: true,
      recipeId: timerData.recipeId,
      stepId: timerData.stepId
    };

    this.extensionState.activeTimers.push(timer);
    await chrome.storage.local.set({ activeTimers: this.extensionState.activeTimers });

    // Set alarm for timer
    chrome.alarms.create(timer.id, {
      delayInMinutes: timerData.duration / 60
    });
  }

  private async stopTimer(timerId: string): Promise<void> {
    this.extensionState.activeTimers = this.extensionState.activeTimers.filter(
      timer => timer.id !== timerId
    );
    await chrome.storage.local.set({ activeTimers: this.extensionState.activeTimers });
    chrome.alarms.clear(timerId);
  }

  private async searchRecipes(query: string): Promise<any[]> {
    // This would integrate with the search service
    // For now, return empty results
    return [];
  }

  private detectRecipeWebsite(tab: chrome.tabs.Tab): void {
    const recipeWebsites = [
      'allrecipes.com',
      'food.com',
      'epicurious.com',
      'foodnetwork.com',
      'tasty.co',
      'delish.com'
    ];

    if (tab.url && recipeWebsites.some(site => tab.url!.includes(site))) {
      // Show page action to extract recipe
      chrome.action.setBadgeText({
        text: '!',
        tabId: tab.id
      });
      chrome.action.setBadgeBackgroundColor({
        color: '#4ade80',
        tabId: tab.id
      });
    }
  }

  private generateRecipeFromSelection(selectedText: string): void {
    chrome.tabs.create({
      url: chrome.runtime.getURL(`options.html#generate?ingredients=${encodeURIComponent(selectedText)}`)
    });
  }

  private openRecipeSearch(): void {
    chrome.tabs.create({
      url: chrome.runtime.getURL('options.html#search')
    });
  }

  private handleTimerAlarm(alarmName: string): void {
    const timerId = alarmName;
    const timer = this.extensionState.activeTimers.find(t => t.id === timerId);
    
    if (timer) {
      // Show timer completion notification
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Timer Complete!',
        message: `${timer.name} timer has finished.`,
        requireInteraction: true
      });

      // Remove completed timer
      this.stopTimer(timerId);
    }
  }

  private performTrendAnalysis(): void {
    // Analyze trending recipes and update recommendations
    console.log('Performing trend analysis...');
  }

  private performDailyCleanup(): void {
    // Clean up old data, optimize storage
    console.log('Performing daily cleanup...');
  }
}

// Initialize the background service
BackgroundService.getInstance();
