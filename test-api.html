<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefAI API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2c3e50;
            color: #ecf0f1;
        }
        .container {
            background: #34495e;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        input, textarea, button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #4a5f7a;
            border-radius: 4px;
            background: #2c3e50;
            color: #ecf0f1;
            font-size: 14px;
        }
        button {
            background: #3498db;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #2980b9;
        }
        button:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
        }
        .result {
            background: #2c3e50;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #4a5f7a;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #e74c3c;
            color: white;
        }
        .success {
            background: #27ae60;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🍳 ChefAI API Test</h1>
    
    <div class="container">
        <h2>API Configuration</h2>
        <input type="password" id="api-key" placeholder="Enter your Gemini API Key">
        <button onclick="testConnection()">Test Connection</button>
        <div id="connection-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h2>Recipe Generation Test</h2>
        <input type="text" id="ingredients" placeholder="Enter ingredients (e.g., chicken, rice, vegetables)" value="chicken, rice, vegetables, soy sauce">
        <button onclick="generateRecipe()" id="generate-btn">Generate Recipe</button>
        <div id="recipe-result" class="result" style="display: none;"></div>
    </div>

    <script src="src/api/gemini-api.js"></script>
    <script>
        let geminiAPI = new GeminiAPI();

        async function testConnection() {
            const apiKey = document.getElementById('api-key').value.trim();
            const resultDiv = document.getElementById('connection-result');
            
            if (!apiKey) {
                showResult(resultDiv, 'Please enter your API key', 'error');
                return;
            }

            // Save API key temporarily for testing
            await chrome.storage.local.set({
                chefai_llm_config: { apiKey: apiKey }
            });

            showResult(resultDiv, 'Testing connection...', '');
            
            try {
                const initialized = await geminiAPI.initialize();
                if (!initialized) {
                    throw new Error('Failed to initialize API');
                }

                const result = await geminiAPI.testConnection();
                
                if (result.success) {
                    showResult(resultDiv, `✅ ${result.message}`, 'success');
                } else {
                    showResult(resultDiv, `❌ ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ Error: ${error.message}`, 'error');
            }
        }

        async function generateRecipe() {
            const ingredients = document.getElementById('ingredients').value.trim();
            const resultDiv = document.getElementById('recipe-result');
            const generateBtn = document.getElementById('generate-btn');
            
            if (!ingredients) {
                showResult(resultDiv, 'Please enter some ingredients', 'error');
                return;
            }

            generateBtn.textContent = 'Generating...';
            generateBtn.disabled = true;
            showResult(resultDiv, 'Generating your recipe...', '');
            
            try {
                const initialized = await geminiAPI.initialize();
                if (!initialized) {
                    throw new Error('API not configured. Please test connection first.');
                }

                const ingredientsList = ingredients.split(',').map(ing => ing.trim());
                const recipe = await geminiAPI.generateRecipe(ingredientsList, {
                    cuisine: 'any',
                    difficulty: 'medium',
                    cookingTime: 45
                });

                const formattedRecipe = formatRecipe(recipe);
                showResult(resultDiv, formattedRecipe, 'success');
                
            } catch (error) {
                showResult(resultDiv, `❌ Error: ${error.message}`, 'error');
            } finally {
                generateBtn.textContent = 'Generate Recipe';
                generateBtn.disabled = false;
            }
        }

        function formatRecipe(recipe) {
            let formatted = `🍳 ${recipe.title}\n\n`;
            
            if (recipe.description) {
                formatted += `📝 Description:\n${recipe.description}\n\n`;
            }
            
            if (recipe.ingredients && recipe.ingredients.length > 0) {
                formatted += `📋 Ingredients:\n`;
                recipe.ingredients.forEach(ing => {
                    formatted += `• ${ing}\n`;
                });
                formatted += '\n';
            }
            
            if (recipe.instructions && recipe.instructions.length > 0) {
                formatted += `👨‍🍳 Instructions:\n`;
                recipe.instructions.forEach((inst, index) => {
                    formatted += `${index + 1}. ${inst}\n`;
                });
                formatted += '\n';
            }
            
            if (recipe.cookingInfo && Object.keys(recipe.cookingInfo).length > 0) {
                formatted += `ℹ️ Cooking Information:\n`;
                Object.entries(recipe.cookingInfo).forEach(([key, value]) => {
                    formatted += `${key.replace('_', ' ')}: ${value}\n`;
                });
            }
            
            return formatted;
        }

        function showResult(element, message, type) {
            element.style.display = 'block';
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('ChefAI API Test page loaded');
        });
    </script>
</body>
</html>
