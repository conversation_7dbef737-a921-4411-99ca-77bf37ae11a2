// ChefAI Dashboard with Real-time Updates and Customization
document.addEventListener('DOMContentLoaded', async function() {
  console.log('ChefAI Dashboard loaded');
  
  // Load services first
  await loadServices();
  
  // Initialize dashboard components
  initializeDashboard();
  
  // Set up navigation
  setupNavigation();
  
  // Load initial data
  loadDashboardData();
  
  // Set up real-time updates
  setupRealtimeUpdates();
});

// Load required services
async function loadServices() {
  try {
    // Load service scripts
    await Promise.all([
      loadScript('src/services/realtime-manager.js'),
      loadScript('src/services/customization-manager.js'),
      loadScript('src/services/notification-manager.js'),
      loadScript('src/components/customization-panel.js')
    ]);
    
    // Initialize services
    if (window.realtimeManager) {
      await window.realtimeManager.initialize();
    }
    
    if (window.customizationManager) {
      await window.customizationManager.initialize();
    }
    
    if (window.notificationManager) {
      await window.notificationManager.initialize();
    }
    
    console.log('Dashboard services loaded successfully');
    
  } catch (error) {
    console.error('Error loading dashboard services:', error);
  }
}

// Load script helper
function loadScript(src) {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (document.querySelector(`script[src="${src}"]`)) {
      resolve();
      return;
    }
    
    const script = document.createElement('script');
    script.src = src;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

// Initialize dashboard
function initializeDashboard() {
  // Apply current theme
  if (window.customizationManager) {
    const currentTheme = window.customizationManager.getCurrentTheme();
    if (currentTheme) {
      applyThemeToDashboard(currentTheme);
    }
  }
  
  // Show welcome notification
  if (window.notificationManager) {
    window.notificationManager.show({
      type: 'info',
      title: 'Welcome to ChefAI Dashboard',
      message: 'All systems are ready and real-time updates are active',
      duration: 4000
    });
  }
}

// Set up navigation
function setupNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  const contentSections = document.querySelectorAll('.content-section');
  
  navItems.forEach(item => {
    item.addEventListener('click', (e) => {
      e.preventDefault();
      
      const targetSection = item.dataset.section;
      
      // Update active nav item
      navItems.forEach(nav => nav.classList.remove('active'));
      item.classList.add('active');
      
      // Show target section
      contentSections.forEach(section => {
        section.style.display = 'none';
      });
      
      const targetElement = document.getElementById(targetSection);
      if (targetElement) {
        targetElement.style.display = 'block';
        
        // Initialize section-specific functionality
        initializeSection(targetSection);
      }
    });
  });
  
  // Initialize first section
  const firstNav = document.querySelector('.nav-item');
  if (firstNav) {
    firstNav.click();
  }
}

// Initialize specific sections
async function initializeSection(sectionId) {
  switch (sectionId) {
    case 'customization':
      await initializeCustomizationSection();
      break;
    case 'analytics':
      initializeAnalyticsSection();
      break;
    case 'settings':
      initializeSettingsSection();
      break;
    default:
      console.log(`Section ${sectionId} initialized`);
  }
}

// Initialize customization section
async function initializeCustomizationSection() {
  const customizationContainer = document.getElementById('customization-content');
  if (!customizationContainer || !window.CustomizationPanel) return;
  
  try {
    const customizationPanel = new window.CustomizationPanel(customizationContainer);
    await customizationPanel.initialize();
    
    console.log('Customization panel initialized');
  } catch (error) {
    console.error('Error initializing customization panel:', error);
  }
}

// Initialize analytics section
function initializeAnalyticsSection() {
  // Load analytics data
  loadAnalyticsData();
  
  // Set up real-time analytics updates
  if (window.realtimeManager) {
    window.realtimeManager.on('recipe_generated', (data) => {
      updateAnalyticsCounters();
    });
  }
}

// Initialize settings section
function initializeSettingsSection() {
  // Load current settings
  loadCurrentSettings();
  
  // Set up settings form handlers
  setupSettingsHandlers();
}

// Load dashboard data
async function loadDashboardData() {
  try {
    // Load recent recipes
    await loadRecentRecipes();
    
    // Load statistics
    await loadStatistics();
    
    // Load user preferences
    await loadUserPreferences();
    
  } catch (error) {
    console.error('Error loading dashboard data:', error);
  }
}

// Load recent recipes
async function loadRecentRecipes() {
  try {
    const result = await chrome.storage.local.get(['chefai_recent_recipes']);
    const recentRecipes = result.chefai_recent_recipes || [];
    
    const recipesContainer = document.getElementById('recent-recipes');
    if (recipesContainer) {
      recipesContainer.innerHTML = recentRecipes.length > 0 
        ? recentRecipes.map(recipe => createRecipeCard(recipe)).join('')
        : '<p>No recent recipes found. Generate your first recipe!</p>';
    }
  } catch (error) {
    console.error('Error loading recent recipes:', error);
  }
}

// Create recipe card HTML
function createRecipeCard(recipe) {
  return `
    <div class="recipe-card">
      <h4>${recipe.title || 'Untitled Recipe'}</h4>
      <p>${recipe.description || 'No description available'}</p>
      <div class="recipe-meta">
        <span>🕒 ${new Date(recipe.timestamp).toLocaleDateString()}</span>
        <span>🥘 ${recipe.ingredients?.length || 0} ingredients</span>
      </div>
    </div>
  `;
}

// Load statistics
async function loadStatistics() {
  try {
    const result = await chrome.storage.local.get([
      'chefai_stats_recipes_generated',
      'chefai_stats_api_calls',
      'chefai_stats_last_used'
    ]);
    
    // Update statistics display
    updateStatistic('recipes-count', result.chefai_stats_recipes_generated || 0);
    updateStatistic('api-calls-count', result.chefai_stats_api_calls || 0);
    updateStatistic('last-used', result.chefai_stats_last_used 
      ? new Date(result.chefai_stats_last_used).toLocaleDateString()
      : 'Never'
    );
    
  } catch (error) {
    console.error('Error loading statistics:', error);
  }
}

// Update statistic display
function updateStatistic(elementId, value) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = value;
  }
}

// Load user preferences
async function loadUserPreferences() {
  try {
    const result = await chrome.storage.local.get(['chefai_user_preferences']);
    const preferences = result.chefai_user_preferences || {};
    
    // Apply preferences to dashboard
    if (preferences.compactMode) {
      document.body.classList.add('compact-mode');
    }
    
    if (preferences.darkMode !== false) {
      document.body.classList.add('dark-mode');
    }
    
  } catch (error) {
    console.error('Error loading user preferences:', error);
  }
}

// Set up real-time updates
function setupRealtimeUpdates() {
  if (!window.realtimeManager) return;
  
  // Listen for recipe generation
  window.realtimeManager.on('recipe_generated', (data) => {
    // Update recent recipes
    loadRecentRecipes();
    
    // Update statistics
    loadStatistics();
    
    // Show notification
    if (window.notificationManager) {
      window.notificationManager.show({
        type: 'recipe',
        title: 'New Recipe Generated',
        message: `${data.title || 'Recipe'} has been created`,
        duration: 5000,
        actions: [
          {
            label: 'View',
            action: () => showRecipeDetails(data)
          }
        ]
      });
    }
  });
  
  // Listen for settings updates
  window.realtimeManager.on('settings_updated', (data) => {
    loadUserPreferences();
    
    if (window.notificationManager) {
      window.notificationManager.show({
        type: 'info',
        title: 'Settings Updated',
        message: 'Your preferences have been synchronized',
        duration: 3000
      });
    }
  });
  
  // Listen for theme changes
  window.realtimeManager.on('theme_changed', (data) => {
    if (window.customizationManager) {
      const theme = window.customizationManager.getCurrentTheme();
      if (theme) {
        applyThemeToDashboard(theme);
      }
    }
  });
  
  // Listen for connection status
  window.realtimeManager.on('connection_status', (data) => {
    updateConnectionStatus(data.status);
  });
}

// Apply theme to dashboard
function applyThemeToDashboard(theme) {
  const root = document.documentElement;
  
  // Apply theme colors
  Object.entries(theme.colors).forEach(([key, value]) => {
    root.style.setProperty(`--chefai-color-${key}`, value);
  });
  
  // Apply fonts
  Object.entries(theme.fonts).forEach(([key, value]) => {
    root.style.setProperty(`--chefai-font-${key}`, value);
  });
  
  // Apply spacing
  Object.entries(theme.spacing).forEach(([key, value]) => {
    root.style.setProperty(`--chefai-spacing-${key}`, value);
  });
  
  // Apply border radius
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    root.style.setProperty(`--chefai-radius-${key}`, value);
  });
  
  // Apply shadows
  Object.entries(theme.shadows).forEach(([key, value]) => {
    root.style.setProperty(`--chefai-shadow-${key}`, value);
  });
  
  console.log(`Applied theme: ${theme.name}`);
}

// Update connection status
function updateConnectionStatus(status) {
  const statusElement = document.getElementById('connection-status');
  if (statusElement) {
    statusElement.className = `status-indicator ${status}`;
    statusElement.title = `Connection: ${status}`;
  }
}

// Show recipe details
function showRecipeDetails(recipe) {
  // Implementation for showing recipe details
  console.log('Showing recipe details:', recipe);
}

// Load analytics data
function loadAnalyticsData() {
  // Implementation for loading analytics
  console.log('Loading analytics data...');
}

// Update analytics counters
function updateAnalyticsCounters() {
  // Implementation for updating analytics
  console.log('Updating analytics counters...');
}

// Load current settings
function loadCurrentSettings() {
  // Implementation for loading settings
  console.log('Loading current settings...');
}

// Set up settings handlers
function setupSettingsHandlers() {
  // Implementation for settings handlers
  console.log('Setting up settings handlers...');
}

// Export functions for external use
window.dashboardAPI = {
  loadServices,
  initializeDashboard,
  setupRealtimeUpdates,
  applyThemeToDashboard
};
