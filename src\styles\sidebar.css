/* Sidebar Interface Styles */
.sidebar-interface {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.sidebar-interface.visible {
  opacity: 1;
  visibility: visible;
}

/* Enhanced Glassmorphism Effects */
.glass-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Settings Panel Enhancements */
.settings-panel {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.settings-panel .checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.settings-panel .checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-panel .checkbox-label:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.settings-panel .checkbox-label input[type="checkbox"] {
  margin: 0;
  accent-color: #667eea;
}

.settings-panel .provider-description {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  margin-top: 5px;
  display: block;
}

.settings-panel .settings-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.settings-panel .test-result {
  margin-top: 15px;
  padding: 12px;
  border-radius: 8px;
  font-weight: 500;
}

.settings-panel .test-result.success {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  color: #4CAF50;
}

.settings-panel .test-result.error {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
  color: #f44336;
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.sidebar-content {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: #2c3e50;
  border-left: 1px solid #34495e;
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  color: white;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 10001;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.sidebar-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  pointer-events: none;
  z-index: 1;
}

.sidebar-content > * {
  position: relative;
  z-index: 2;
}

.sidebar-content.visible {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #4a5f7a;
  background: #34495e;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sidebar-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  background: transparent;
  border: none;
  color: #bdc3c7;
  font-size: 18px;
  cursor: pointer;
  padding: 6px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.close-btn:hover::before {
  opacity: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

/* Enhanced Sidebar Tabs */
.sidebar-tabs {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  position: relative;
  padding: 0 8px;
}

.sidebar-tabs::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 18px 12px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.75);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.9em;
  font-weight: 500;
  position: relative;
  border-radius: 12px 12px 0 0;
  margin: 0 2px;
}

.tab::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
  border-radius: 2px 2px 0 0;
}

.tab.active {
  color: white;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.15));
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
  transform: translateY(-2px);
}

.tab.active::before {
  width: 80%;
}

.tab:hover:not(.active) {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.08);
}

.tab:hover:not(.active)::before {
  width: 40%;
  opacity: 0.6;
}

.tab-icon {
  font-size: 1.3em;
  transition: transform 0.3s ease;
}

.tab.active .tab-icon {
  transform: scale(1.1);
}

/* Tab Content */
.tab-content {
  padding: 20px;
  height: calc(100vh - 140px);
  overflow-y: auto;
}

/* Generate Tab Styles */
.form-section h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 1em;
  color: rgba(255, 255, 255, 0.95);
}

.input-description {
  margin: 0 0 12px 0;
  font-size: 0.85em;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  font-style: italic;
}

.glass-input,
.glass-select,
.glass-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
}

.glass-input::placeholder,
.glass-textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.glass-input:focus,
.glass-select:focus,
.glass-textarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.glass-select option {
  background: #333;
  color: white;
}

.glass-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
}

.glass-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #FFD700;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.glass-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #FFD700;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Enhanced Ingredient Input Section */
.ingredient-input {
  display: flex;
  gap: 12px;
  align-items: stretch;
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ingredient-input input {
  flex: 1;
  font-size: 16px;
  padding: 14px 16px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s ease;
}

.ingredient-input input:focus {
  border-color: #FFD700;
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
}

.ingredient-input input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

.add-btn {
  padding: 14px 24px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border: none;
  border-radius: 10px;
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.add-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #FFA500, #FF8C00);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
}

.add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 60px;
  align-items: flex-start;
  align-content: flex-start;
}

.ingredients-list:empty::before {
  content: "🥘 Your ingredients will appear here...";
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  width: 100%;
  text-align: center;
  padding: 20px 0;
}

.ingredient-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 25px;
  font-size: 0.9em;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: slideIn 0.3s ease-out;
}

.ingredient-tag:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.remove-ingredient {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  font-weight: bold;
}

.remove-ingredient:hover {
  background: rgba(255, 100, 100, 0.8);
  transform: scale(1.1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Ingredient Suggestions */
.ingredient-suggestions {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.suggestions-label {
  margin: 0 0 12px 0;
  font-size: 0.9em;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-tag {
  padding: 6px 12px;
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 20px;
  color: white;
  font-size: 0.85em;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.suggestion-tag:hover {
  background: rgba(255, 215, 0, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.2);
}

/* Parameters Grid */
.parameters-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.parameters-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* Dietary Options */
.dietary-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.checkbox-label:hover {
  background: rgba(255, 255, 255, 0.1);
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #FFD700;
}

/* Buttons */
.glass-button {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.glass-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.glass-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.glass-button.primary {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border-color: #4CAF50;
}

.glass-button.primary:hover:not(:disabled) {
  background: linear-gradient(45deg, #45a049, #3d8b40);
}

.generate-btn {
  width: 100%;
  padding: 20px 28px;
  font-size: 1.2em;
  font-weight: 600;
  margin-top: 28px;
  background: linear-gradient(135deg, #4CAF50, #45a049, #2E7D32);
  border: none;
  border-radius: 16px;
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.35);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.generate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.generate-btn:hover:not(:disabled)::before {
  left: 100%;
}

.generate-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #2E7D32, #1B5E20);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.generate-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-icon {
  font-size: 1.2em;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.warning-message {
  background: rgba(255, 152, 0, 0.2);
  border: 1px solid rgba(255, 152, 0, 0.5);
  border-radius: 8px;
  padding: 12px;
  margin-top: 15px;
  font-size: 0.9em;
  text-align: center;
}

/* Settings Tab */
.llm-settings h3 {
  margin: 0 0 20px;
  font-size: 1.3em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.provider-description {
  display: block;
  margin-top: 5px;
  font-size: 0.8em;
  opacity: 0.8;
  line-height: 1.4;
}

.settings-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.test-btn,
.save-btn {
  flex: 1;
}

.test-result {
  padding: 12px;
  border-radius: 8px;
  margin-top: 15px;
  font-size: 0.9em;
  text-align: center;
}

.test-result.success {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
}

.test-result.error {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
}

/* History Tab */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  opacity: 0.8;
}

.empty-state p {
  margin: 10px 0;
  line-height: 1.5;
}

.recipes-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recipe-item {
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.recipe-item h4 {
  margin: 0 0 8px;
  font-size: 1.1em;
}

.recipe-item p {
  margin: 0 0 12px;
  opacity: 0.9;
  font-size: 0.9em;
  line-height: 1.4;
}

.recipe-meta {
  display: flex;
  gap: 12px;
  margin: 12px 0;
  font-size: 0.8em;
  opacity: 0.8;
}

.view-recipe-btn {
  width: 100%;
  margin-top: 10px;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .sidebar-content {
    width: 100%;
    border-left: none;
    border-radius: 0;
  }

  .sidebar-header {
    padding: 20px 16px;
  }

  .sidebar-header h2 {
    font-size: 1.4em;
  }

  .parameters-grid,
  .parameters-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .dietary-options,
  .checkbox-grid {
    grid-template-columns: 1fr;
  }

  .settings-actions {
    flex-direction: column;
    gap: 12px;
  }

  .ingredient-input {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .tab {
    font-size: 0.8em;
    padding: 12px 8px;
    gap: 4px;
  }

  .tab-icon {
    font-size: 1.1em;
  }

  .generate-btn {
    padding: 16px 20px;
    font-size: 1.1em;
  }
}

@media (max-width: 480px) {
  .sidebar-content {
    width: 100vw;
  }

  .sidebar-tabs {
    padding: 0 4px;
  }

  .tab {
    font-size: 0.75em;
    padding: 10px 4px;
    margin: 0 1px;
  }

  .tab-content {
    padding: 16px;
  }

  .input-group {
    margin-bottom: 16px;
  }

  .glass-input,
  .glass-select,
  .glass-textarea {
    padding: 10px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Enhanced Scrollbar Styling */
.sidebar-content::-webkit-scrollbar,
.tab-content::-webkit-scrollbar {
  width: 8px;
}

.sidebar-content::-webkit-scrollbar-track,
.tab-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.sidebar-content::-webkit-scrollbar-thumb,
.tab-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-content::-webkit-scrollbar-thumb:hover,
.tab-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.4));
}

/* Enhanced Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.sidebar-content.visible {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-content > * {
  animation: fadeInUp 0.3s ease-out;
}

.generate-btn:active {
  animation: pulse 0.2s ease-in-out;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Enhanced Focus States */
.glass-input:focus,
.glass-select:focus,
.glass-textarea:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.6);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
}

.glass-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Enhanced Hover Effects */
.setting-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.input-group:hover label {
  color: rgba(255, 255, 255, 1);
  transition: color 0.3s ease;
}
