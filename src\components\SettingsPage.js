// ChefAI Settings Page Component
class SettingsPage {
  constructor() {
    this.currentProvider = 'gemini';
    this.providers = {
      gemini: {
        name: 'Gemini',
        models: ['gemini-2.5-flash-preview-05-20', 'gemini-2.5-pro-preview-06-05', 'gemini-2.0-flash'],
        apiKey: '',
        selectedModel: 'gemini-2.5-flash-preview-05-20'
      },
      openrouter: {
        name: 'OpenRouter',
        models: ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet'],
        apiKey: '',
        selectedModel: 'gpt-4'
      },
      openai: {
        name: 'OpenAI',
        models: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'],
        apiKey: '',
        selectedModel: 'gpt-4'
      }
    };
    this.settings = {
      temperature: 0.7,
      topP: 0.9,
      maxTokens: 2000
    };
  }

  render() {
    return `
      <div class="settings-container" style="
        display: flex;
        height: 100vh;
        background: #2c3e50;
        color: #ecf0f1;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      ">
        <!-- Sidebar Navigation -->
        <div class="settings-sidebar" style="
          width: 200px;
          background: #34495e;
          border-right: 1px solid #4a5f7a;
          padding: 20px 0;
        ">
          <div style="padding: 0 20px 20px 20px;">
            <h2 style="
              margin: 0;
              font-size: 18px;
              font-weight: 600;
              color: #ecf0f1;
            ">Settings</h2>
          </div>

          <nav>
            <a href="#general" class="nav-item active" style="
              display: flex;
              align-items: center;
              padding: 12px 20px;
              color: #ecf0f1;
              text-decoration: none;
              font-size: 14px;
              border-left: 3px solid #3498db;
              background: rgba(52, 152, 219, 0.1);
              gap: 10px;
            ">
              <span style="font-size: 16px;">⚙️</span>
              General
            </a>

            <a href="#models" class="nav-item" style="
              display: flex;
              align-items: center;
              padding: 12px 20px;
              color: #bdc3c7;
              text-decoration: none;
              font-size: 14px;
              border-left: 3px solid transparent;
              gap: 10px;
            ">
              <span style="font-size: 16px;">🤖</span>
              Models
            </a>

            <a href="#firewall" class="nav-item" style="
              display: flex;
              align-items: center;
              padding: 12px 20px;
              color: #bdc3c7;
              text-decoration: none;
              font-size: 14px;
              border-left: 3px solid transparent;
              gap: 10px;
            ">
              <span style="font-size: 16px;">🔒</span>
              Firewall
            </a>

            <a href="#help" class="nav-item" style="
              display: flex;
              align-items: center;
              padding: 12px 20px;
              color: #bdc3c7;
              text-decoration: none;
              font-size: 14px;
              border-left: 3px solid transparent;
              gap: 10px;
            ">
              <span style="font-size: 16px;">❓</span>
              Help
            </a>
          </nav>
        </div>

        <!-- Main Content -->
        <div class="settings-content" style="
          flex: 1;
          padding: 30px;
          overflow-y: auto;
        ">
          ${this.renderMainContent()}
        </div>
      </div>
    `;
  }

  renderMainContent() {
    return `
      <!-- LLM Providers Section -->
      <div style="margin-bottom: 40px;">
        <h2 style="
          margin: 0 0 24px 0;
          font-size: 24px;
          font-weight: 600;
          color: #ecf0f1;
        ">LLM Providers</h2>

        ${this.renderProviderCard()}
      </div>

      <!-- Model Selection Section -->
      <div style="margin-bottom: 40px;">
        <h2 style="
          margin: 0 0 24px 0;
          font-size: 24px;
          font-weight: 600;
          color: #ecf0f1;
        ">Model Selection</h2>

        ${this.renderModelSelection()}
      </div>
    `;
  }

  renderProviderCard() {
    const provider = this.providers[this.currentProvider];

    return `
      <div style="
        background: #34495e;
        border-radius: 8px;
        padding: 24px;
        border: 1px solid #4a5f7a;
        margin-bottom: 20px;
      ">
        <div style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        ">
          <h3 style="
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #ecf0f1;
          ">${provider.name}</h3>

          <button style="
            background: #e74c3c;
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
          ">Delete</button>
        </div>

        <div style="margin-bottom: 20px;">
          <label style="
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #ecf0f1;
          ">API Key*</label>

          <input type="password" value="${'*'.repeat(30)}" style="
            width: 100%;
            padding: 10px 12px;
            background: #2c3e50;
            border: 1px solid #4a5f7a;
            border-radius: 4px;
            color: #ecf0f1;
            font-size: 14px;
            font-family: monospace;
          ">
        </div>

        <div>
          <label style="
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #ecf0f1;
          ">Models</label>

          <div style="display: flex; flex-wrap: wrap; gap: 8px;">
            ${provider.models.map(model => `
              <span style="
                background: #3498db;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                display: inline-flex;
                align-items: center;
                gap: 4px;
              ">
                ${model}
                <button style="
                  background: none;
                  border: none;
                  color: white;
                  cursor: pointer;
                  font-size: 14px;
                  padding: 0;
                  margin-left: 4px;
                ">×</button>
              </span>
            `).join('')}
          </div>

          <div style="
            margin-top: 12px;
            font-size: 12px;
            color: #7f8c8d;
          ">Type and Press Enter or Space to add</div>
        </div>

        <button style="
          background: #3498db;
          border: none;
          color: white;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          margin-top: 20px;
          width: 100%;
        ">+ Add New Provider</button>
      </div>
    `;
  }

  renderModelSelection() {
    const provider = this.providers[this.currentProvider];

    return `
      <div style="
        background: #34495e;
        border-radius: 8px;
        padding: 24px;
        border: 1px solid #4a5f7a;
      ">
        <div style="
          background: #2c3e50;
          border-radius: 6px;
          padding: 20px;
          margin-bottom: 24px;
          border: 1px solid #34495e;
        ">
          <h4 style="
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #ecf0f1;
          ">Planner</h4>

          <p style="
            margin: 0 0 20px 0;
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.4;
          ">Develops and refines strategies to complete tasks</p>

          <div style="margin-bottom: 20px;">
            <label style="
              display: block;
              margin-bottom: 8px;
              font-size: 14px;
              font-weight: 500;
              color: #ecf0f1;
            ">Model</label>

            <select style="
              width: 100%;
              padding: 10px 12px;
              background: #34495e;
              border: 1px solid #4a5f7a;
              border-radius: 4px;
              color: #ecf0f1;
              font-size: 14px;
            ">
              ${provider.models.map(model => `
                <option value="${model}" ${model === provider.selectedModel ? 'selected' : ''}>
                  ${provider.name} > ${model}
                </option>
              `).join('')}
            </select>
          </div>

          <div style="margin-bottom: 20px;">
            <label style="
              display: block;
              margin-bottom: 8px;
              font-size: 14px;
              font-weight: 500;
              color: #ecf0f1;
            ">Temperature</label>

            <div style="display: flex; align-items: center; gap: 16px;">
              <input type="range" min="0" max="1" step="0.1" value="${this.settings.temperature}" style="
                flex: 1;
                height: 4px;
                background: #4a5f7a;
                border-radius: 2px;
                outline: none;
              ">
              <div style="
                display: flex;
                gap: 8px;
                align-items: center;
              ">
                <span style="
                  font-size: 14px;
                  color: #bdc3c7;
                  min-width: 40px;
                ">${this.settings.temperature}</span>
                <span style="
                  font-size: 14px;
                  color: #7f8c8d;
                ">0.7</span>
              </div>
            </div>
          </div>

          <div>
            <label style="
              display: block;
              margin-bottom: 8px;
              font-size: 14px;
              font-weight: 500;
              color: #ecf0f1;
            ">Top P</label>

            <div style="display: flex; align-items: center; gap: 16px;">
              <input type="range" min="0" max="1" step="0.1" value="${this.settings.topP}" style="
                flex: 1;
                height: 4px;
                background: #4a5f7a;
                border-radius: 2px;
                outline: none;
              ">
              <div style="
                display: flex;
                gap: 8px;
                align-items: center;
              ">
                <span style="
                  font-size: 14px;
                  color: #bdc3c7;
                  min-width: 40px;
                ">${this.settings.topP}</span>
                <span style="
                  font-size: 14px;
                  color: #7f8c8d;
                ">0.9</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.SettingsPage = SettingsPage;
}