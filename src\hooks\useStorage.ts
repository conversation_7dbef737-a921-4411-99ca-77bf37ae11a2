import { useState, useEffect, useCallback } from 'react';

type StorageArea = 'local' | 'sync';

export const useStorage = <T>(
  key: string,
  defaultValue?: T,
  storageArea: StorageArea = 'local'
) => {
  const [data, setData] = useState<T | undefined>(defaultValue);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get the appropriate storage area
  const getStorageArea = useCallback(() => {
    return storageArea === 'sync' ? chrome.storage.sync : chrome.storage.local;
  }, [storageArea]);

  // Load data from storage
  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const storage = getStorageArea();
      const result = await storage.get([key]);
      
      if (result[key] !== undefined) {
        setData(result[key]);
      } else if (defaultValue !== undefined) {
        setData(defaultValue);
        // Save default value to storage
        await storage.set({ [key]: defaultValue });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load data';
      setError(errorMessage);
      console.error(`Failed to load data for key "${key}":`, err);
    } finally {
      setIsLoading(false);
    }
  }, [key, defaultValue, getStorageArea]);

  // Save data to storage
  const updateData = useCallback(async (newData: T) => {
    try {
      setError(null);
      
      const storage = getStorageArea();
      await storage.set({ [key]: newData });
      setData(newData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save data';
      setError(errorMessage);
      console.error(`Failed to save data for key "${key}":`, err);
    }
  }, [key, getStorageArea]);

  // Remove data from storage
  const removeData = useCallback(async () => {
    try {
      setError(null);
      
      const storage = getStorageArea();
      await storage.remove([key]);
      setData(undefined);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove data';
      setError(errorMessage);
      console.error(`Failed to remove data for key "${key}":`, err);
    }
  }, [key, getStorageArea]);

  // Clear all data from storage area
  const clearAllData = useCallback(async () => {
    try {
      setError(null);
      
      const storage = getStorageArea();
      await storage.clear();
      setData(undefined);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear data';
      setError(errorMessage);
      console.error('Failed to clear storage:', err);
    }
  }, [getStorageArea]);

  // Listen for storage changes
  useEffect(() => {
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }, areaName: string) => {
      if (areaName === storageArea && changes[key]) {
        const change = changes[key];
        if (change.newValue !== undefined) {
          setData(change.newValue);
        } else {
          setData(undefined);
        }
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);
    
    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, [key, storageArea]);

  // Load initial data
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    updateData,
    removeData,
    clearAllData,
    isLoading,
    error,
    reload: loadData
  };
};
