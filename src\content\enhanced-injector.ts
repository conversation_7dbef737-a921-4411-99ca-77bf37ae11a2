// Enhanced Content Script - Integrates ChefAI Dashboard with web pages
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ChefAIApp } from '../components/ChefAIApp';

interface InjectorState {
  isVisible: boolean;
  isInitialized: boolean;
  container: HTMLElement | null;
  floatingButton: HTMLElement | null;
  reactRoot: any;
  currentMode: 'dashboard' | 'sidebar';
}

class EnhancedChefAIInjector {
  private state: InjectorState = {
    isVisible: false,
    isInitialized: false,
    container: null,
    floatingButton: null,
    reactRoot: null,
    currentMode: 'dashboard'
  };

  constructor() {
    this.init();
  }

  private async init(): Promise<void> {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupInterface());
    } else {
      this.setupInterface();
    }

    // Listen for messages from extension
    this.setupMessageListeners();
    
    // Setup keyboard shortcuts
    this.setupKeyboardShortcuts();
  }

  private setupInterface(): void {
    try {
      this.createFloatingButton();
      this.loadStyles();
      this.state.isInitialized = true;
    } catch (error) {
      console.error('Failed to setup ChefAI interface:', error);
    }
  }

  private createFloatingButton(): void {
    // Remove existing button if any
    if (this.state.floatingButton) {
      this.state.floatingButton.remove();
    }

    this.state.floatingButton = document.createElement('div');
    this.state.floatingButton.id = 'chefai-floating-button';
    this.state.floatingButton.innerHTML = '🍳';
    this.state.floatingButton.title = 'Open ChefAI Dashboard (Ctrl+Shift+C)';

    // Apply enhanced styles
    Object.assign(this.state.floatingButton.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '64px',
      height: '64px',
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      color: 'white',
      fontSize: '28px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: '9999',
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
      border: '2px solid rgba(255, 255, 255, 0.2)',
      backdropFilter: 'blur(10px)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      userSelect: 'none',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    });

    // Add hover effects
    this.state.floatingButton.addEventListener('mouseenter', () => {
      if (this.state.floatingButton) {
        Object.assign(this.state.floatingButton.style, {
          transform: 'scale(1.1) rotate(5deg)',
          boxShadow: '0 12px 35px rgba(0, 0, 0, 0.4)',
          background: 'linear-gradient(135deg, #7c8df0 0%, #8a5ab2 50%, #f5a3fc 100%)'
        });
      }
    });

    this.state.floatingButton.addEventListener('mouseleave', () => {
      if (this.state.floatingButton) {
        Object.assign(this.state.floatingButton.style, {
          transform: 'scale(1) rotate(0deg)',
          boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)'
        });
      }
    });

    // Add click handler
    this.state.floatingButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.toggleInterface();
    });

    // Add to page
    document.body.appendChild(this.state.floatingButton);
  }

  private loadStyles(): void {
    const stylesheets = [
      'src/styles/sidebar.css',
      'src/styles/dashboard.css',
      'src/styles/customization.css'
    ];

    stylesheets.forEach(href => {
      if (!document.querySelector(`link[href*="${href}"]`)) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = chrome.runtime.getURL(href);
        document.head.appendChild(link);
      }
    });
  }

  private setupMessageListeners(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      try {
        switch (message.action) {
          case 'toggleInterface':
            this.toggleInterface();
            sendResponse({ success: true });
            break;
          case 'showInterface':
            this.showInterface(message.mode || 'dashboard');
            sendResponse({ success: true });
            break;
          case 'hideInterface':
            this.hideInterface();
            sendResponse({ success: true });
            break;
          case 'switchMode':
            this.switchMode(message.mode);
            sendResponse({ success: true });
            break;
          case 'getStatus':
            sendResponse({
              success: true,
              status: {
                isVisible: this.state.isVisible,
                isInitialized: this.state.isInitialized,
                currentMode: this.state.currentMode
              }
            });
            break;
          default:
            sendResponse({ success: false, error: 'Unknown action' });
        }
      } catch (error) {
        console.error('Error handling message:', error);
        sendResponse({ success: false, error: error.message });
      }
    });
  }

  private setupKeyboardShortcuts(): void {
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+C to toggle interface
      if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
        e.preventDefault();
        this.toggleInterface();
      }
      
      // Escape to close interface
      if (e.code === 'Escape' && this.state.isVisible) {
        e.preventDefault();
        this.hideInterface();
      }
    });
  }

  private toggleInterface(): void {
    if (this.state.isVisible) {
      this.hideInterface();
    } else {
      this.showInterface();
    }
  }

  private showInterface(mode: 'dashboard' | 'sidebar' = 'dashboard'): void {
    if (!this.state.isInitialized) {
      console.warn('ChefAI interface not initialized');
      return;
    }

    try {
      // Create container if it doesn't exist
      if (!this.state.container) {
        this.createContainer();
      }

      // Update mode
      this.state.currentMode = mode;

      // Render React app
      this.renderApp();

      // Show interface
      this.state.isVisible = true;
      
      // Hide floating button when interface is open
      if (this.state.floatingButton) {
        this.state.floatingButton.style.display = 'none';
      }

      // Add body class for styling
      document.body.classList.add('chefai-interface-open');

    } catch (error) {
      console.error('Failed to show ChefAI interface:', error);
    }
  }

  private hideInterface(): void {
    try {
      if (this.state.container) {
        this.state.container.remove();
        this.state.container = null;
      }

      if (this.state.reactRoot) {
        this.state.reactRoot.unmount();
        this.state.reactRoot = null;
      }

      this.state.isVisible = false;

      // Show floating button
      if (this.state.floatingButton) {
        this.state.floatingButton.style.display = 'flex';
      }

      // Remove body class
      document.body.classList.remove('chefai-interface-open');

    } catch (error) {
      console.error('Failed to hide ChefAI interface:', error);
    }
  }

  private switchMode(mode: 'dashboard' | 'sidebar'): void {
    if (this.state.currentMode !== mode) {
      this.state.currentMode = mode;
      if (this.state.isVisible) {
        this.renderApp();
      }
    }
  }

  private createContainer(): void {
    this.state.container = document.createElement('div');
    this.state.container.id = 'chefai-app-container';
    this.state.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10000;
      pointer-events: auto;
    `;
    document.body.appendChild(this.state.container);
  }

  private renderApp(): void {
    if (!this.state.container) return;

    try {
      // Create React root if it doesn't exist
      if (!this.state.reactRoot) {
        this.state.reactRoot = ReactDOM.createRoot(this.state.container);
      }

      // Render the app
      this.state.reactRoot.render(
        React.createElement(ChefAIApp, {
          initialMode: this.state.currentMode,
          onClose: () => this.hideInterface()
        })
      );
    } catch (error) {
      console.error('Failed to render ChefAI app:', error);
    }
  }

  // Public methods for external access
  public getStatus() {
    return {
      isVisible: this.state.isVisible,
      isInitialized: this.state.isInitialized,
      currentMode: this.state.currentMode
    };
  }

  public destroy(): void {
    try {
      this.hideInterface();
      
      if (this.state.floatingButton) {
        this.state.floatingButton.remove();
        this.state.floatingButton = null;
      }

      document.body.classList.remove('chefai-interface-open');
      
      this.state.isInitialized = false;
    } catch (error) {
      console.error('Failed to destroy ChefAI injector:', error);
    }
  }
}

// Initialize the injector
let chefAIInjector: EnhancedChefAIInjector | null = null;

// Only initialize if we're in a valid context
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  chefAIInjector = new EnhancedChefAIInjector();
}

// Export for potential external access
(window as any).ChefAIInjector = chefAIInjector;

export default chefAIInjector;
