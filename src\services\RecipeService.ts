// Recipe Service - Manages recipe storage, retrieval, and organization
import { Recipe } from '../types';

export interface RecipeFilter {
  cuisine?: string;
  difficulty?: string;
  maxCookTime?: number;
  dietaryRestrictions?: string[];
  source?: string;
  tags?: string[];
}

export interface RecipeSearchOptions {
  query?: string;
  filter?: RecipeFilter;
  sortBy?: 'date' | 'rating' | 'cookTime' | 'title';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export class RecipeService {
  private static instance: RecipeService;
  private readonly STORAGE_KEY = 'chefai_recipes';
  private readonly FAVORITES_KEY = 'chefai_favorites';
  private readonly RATINGS_KEY = 'chefai_ratings';

  private constructor() {}

  public static getInstance(): RecipeService {
    if (!RecipeService.instance) {
      RecipeService.instance = new RecipeService();
    }
    return RecipeService.instance;
  }

  public async saveRecipe(recipe: Recipe): Promise<void> {
    try {
      const recipes = await this.getRecipes();
      
      // Check if recipe already exists (update) or is new
      const existingIndex = recipes.findIndex(r => r.id === recipe.id);
      
      if (existingIndex >= 0) {
        recipes[existingIndex] = { ...recipe, updatedAt: new Date() };
      } else {
        recipes.push({ ...recipe, createdAt: new Date() });
      }

      await chrome.storage.local.set({
        [this.STORAGE_KEY]: recipes
      });
    } catch (error) {
      console.error('Failed to save recipe:', error);
      throw error;
    }
  }

  public async getRecipes(options?: RecipeSearchOptions): Promise<Recipe[]> {
    try {
      const result = await chrome.storage.local.get([this.STORAGE_KEY]);
      let recipes: Recipe[] = result[this.STORAGE_KEY] || [];

      // Apply filters
      if (options?.filter) {
        recipes = this.applyFilters(recipes, options.filter);
      }

      // Apply search query
      if (options?.query) {
        recipes = this.searchRecipes(recipes, options.query);
      }

      // Apply sorting
      if (options?.sortBy) {
        recipes = this.sortRecipes(recipes, options.sortBy, options.sortOrder || 'desc');
      }

      // Apply pagination
      if (options?.offset !== undefined || options?.limit !== undefined) {
        const offset = options.offset || 0;
        const limit = options.limit || recipes.length;
        recipes = recipes.slice(offset, offset + limit);
      }

      return recipes;
    } catch (error) {
      console.error('Failed to get recipes:', error);
      return [];
    }
  }

  public async getRecipeById(id: string): Promise<Recipe | null> {
    try {
      const recipes = await this.getRecipes();
      return recipes.find(r => r.id === id) || null;
    } catch (error) {
      console.error('Failed to get recipe by ID:', error);
      return null;
    }
  }

  public async deleteRecipe(id: string): Promise<void> {
    try {
      const recipes = await this.getRecipes();
      const filteredRecipes = recipes.filter(r => r.id !== id);
      
      await chrome.storage.local.set({
        [this.STORAGE_KEY]: filteredRecipes
      });
    } catch (error) {
      console.error('Failed to delete recipe:', error);
      throw error;
    }
  }

  public async addToFavorites(recipeId: string): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.FAVORITES_KEY]);
      const favorites: string[] = result[this.FAVORITES_KEY] || [];
      
      if (!favorites.includes(recipeId)) {
        favorites.push(recipeId);
        await chrome.storage.local.set({
          [this.FAVORITES_KEY]: favorites
        });
      }
    } catch (error) {
      console.error('Failed to add to favorites:', error);
      throw error;
    }
  }

  public async removeFromFavorites(recipeId: string): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.FAVORITES_KEY]);
      const favorites: string[] = result[this.FAVORITES_KEY] || [];
      
      const updatedFavorites = favorites.filter(id => id !== recipeId);
      await chrome.storage.local.set({
        [this.FAVORITES_KEY]: updatedFavorites
      });
    } catch (error) {
      console.error('Failed to remove from favorites:', error);
      throw error;
    }
  }

  public async getFavorites(): Promise<Recipe[]> {
    try {
      const result = await chrome.storage.local.get([this.FAVORITES_KEY]);
      const favoriteIds: string[] = result[this.FAVORITES_KEY] || [];
      
      const allRecipes = await this.getRecipes();
      return allRecipes.filter(recipe => favoriteIds.includes(recipe.id));
    } catch (error) {
      console.error('Failed to get favorites:', error);
      return [];
    }
  }

  public async rateRecipe(recipeId: string, rating: number): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.RATINGS_KEY]);
      const ratings: Record<string, number> = result[this.RATINGS_KEY] || {};
      
      ratings[recipeId] = Math.max(1, Math.min(5, rating)); // Ensure rating is between 1-5
      
      await chrome.storage.local.set({
        [this.RATINGS_KEY]: ratings
      });
    } catch (error) {
      console.error('Failed to rate recipe:', error);
      throw error;
    }
  }

  public async getRecipeRating(recipeId: string): Promise<number | null> {
    try {
      const result = await chrome.storage.local.get([this.RATINGS_KEY]);
      const ratings: Record<string, number> = result[this.RATINGS_KEY] || {};
      return ratings[recipeId] || null;
    } catch (error) {
      console.error('Failed to get recipe rating:', error);
      return null;
    }
  }

  private applyFilters(recipes: Recipe[], filter: RecipeFilter): Recipe[] {
    return recipes.filter(recipe => {
      if (filter.cuisine && recipe.cuisine !== filter.cuisine) return false;
      if (filter.difficulty && recipe.difficulty !== filter.difficulty) return false;
      if (filter.maxCookTime && recipe.cookTime > filter.maxCookTime) return false;
      if (filter.source && recipe.source !== filter.source) return false;
      
      if (filter.dietaryRestrictions && filter.dietaryRestrictions.length > 0) {
        // Check if recipe meets all dietary restrictions
        const recipeTags = recipe.tags || [];
        const hasAllRestrictions = filter.dietaryRestrictions.every(restriction =>
          recipeTags.some(tag => tag.toLowerCase().includes(restriction.toLowerCase()))
        );
        if (!hasAllRestrictions) return false;
      }
      
      if (filter.tags && filter.tags.length > 0) {
        const recipeTags = recipe.tags || [];
        const hasAnyTag = filter.tags.some(tag =>
          recipeTags.some(recipeTag => recipeTag.toLowerCase().includes(tag.toLowerCase()))
        );
        if (!hasAnyTag) return false;
      }
      
      return true;
    });
  }

  private searchRecipes(recipes: Recipe[], query: string): Recipe[] {
    const searchTerm = query.toLowerCase();
    
    return recipes.filter(recipe => {
      // Search in title
      if (recipe.title.toLowerCase().includes(searchTerm)) return true;
      
      // Search in description
      if (recipe.description?.toLowerCase().includes(searchTerm)) return true;
      
      // Search in ingredients
      if (recipe.ingredients?.some(ing => 
        ing.name?.toLowerCase().includes(searchTerm)
      )) return true;
      
      // Search in tags
      if (recipe.tags?.some(tag => 
        tag.toLowerCase().includes(searchTerm)
      )) return true;
      
      // Search in cuisine
      if (recipe.cuisine?.toLowerCase().includes(searchTerm)) return true;
      
      return false;
    });
  }

  private sortRecipes(recipes: Recipe[], sortBy: string, sortOrder: 'asc' | 'desc'): Recipe[] {
    return recipes.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          comparison = dateA - dateB;
          break;
        case 'cookTime':
          comparison = (a.cookTime || 0) - (b.cookTime || 0);
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'rating':
          // This would require getting ratings, simplified for now
          comparison = 0;
          break;
        default:
          comparison = 0;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }

  public async exportRecipes(): Promise<string> {
    try {
      const recipes = await this.getRecipes();
      const favorites = await chrome.storage.local.get([this.FAVORITES_KEY]);
      const ratings = await chrome.storage.local.get([this.RATINGS_KEY]);
      
      const exportData = {
        recipes,
        favorites: favorites[this.FAVORITES_KEY] || [],
        ratings: ratings[this.RATINGS_KEY] || {},
        exportDate: new Date().toISOString(),
        version: '1.0'
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Failed to export recipes:', error);
      throw error;
    }
  }

  public async importRecipes(data: string): Promise<boolean> {
    try {
      const importData = JSON.parse(data);
      
      if (importData.recipes && Array.isArray(importData.recipes)) {
        await chrome.storage.local.set({
          [this.STORAGE_KEY]: importData.recipes
        });
        
        if (importData.favorites) {
          await chrome.storage.local.set({
            [this.FAVORITES_KEY]: importData.favorites
          });
        }
        
        if (importData.ratings) {
          await chrome.storage.local.set({
            [this.RATINGS_KEY]: importData.ratings
          });
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to import recipes:', error);
      return false;
    }
  }

  public async clearAllRecipes(): Promise<void> {
    try {
      await chrome.storage.local.remove([
        this.STORAGE_KEY,
        this.FAVORITES_KEY,
        this.RATINGS_KEY
      ]);
    } catch (error) {
      console.error('Failed to clear recipes:', error);
      throw error;
    }
  }
}
