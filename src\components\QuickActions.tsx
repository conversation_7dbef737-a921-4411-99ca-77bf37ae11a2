import React from 'react';
import { useI18n } from '../hooks/useI18n';

interface QuickActionsProps {
  onAction: (action: string) => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ onAction }) => {
  const { t } = useI18n();

  const actions = [
    {
      id: 'generate',
      icon: '✨',
      title: t('quick_actions.generate'),
      description: t('quick_actions.generate_desc')
    },
    {
      id: 'search',
      icon: '🔍',
      title: t('quick_actions.search'),
      description: t('quick_actions.search_desc')
    },
    {
      id: 'trending',
      icon: '🔥',
      title: t('quick_actions.trending'),
      description: t('quick_actions.trending_desc')
    },
    {
      id: 'settings',
      icon: '⚙️',
      title: t('quick_actions.settings'),
      description: t('quick_actions.settings_desc')
    }
  ];

  return (
    <section className="quick-actions">
      {actions.map((action) => (
        <div
          key={action.id}
          className="quick-action-card"
          onClick={() => onAction(action.id)}
          role="button"
          tabIndex={0}
          onKeyPress={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              onAction(action.id);
            }
          }}
        >
          <div className="quick-action-icon">{action.icon}</div>
          <h3 className="quick-action-title">{action.title}</h3>
        </div>
      ))}
    </section>
  );
};
