import React, { useState, useEffect } from 'react';
import { DashboardInterface } from './DashboardInterface';
import { SidebarInterface } from './SidebarInterface';
import { LLMService } from '../services/LLMService';
import { AdvancedSettingsService } from '../services/AdvancedSettingsService';
import { RecipeService } from '../services/RecipeService';
import { Recipe } from '../types';

interface ChefAIAppProps {
  initialMode?: 'dashboard' | 'sidebar';
  onClose?: () => void;
}

export const ChefAIApp: React.FC<ChefAIAppProps> = ({ 
  initialMode = 'dashboard',
  onClose 
}) => {
  const [currentMode, setCurrentMode] = useState<'dashboard' | 'sidebar' | 'closed'>(initialMode);
  const [isInitialized, setIsInitialized] = useState(false);
  const [systemReady, setSystemReady] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);

  const llmService = LLMService.getInstance();
  const advancedSettingsService = AdvancedSettingsService.getInstance();
  const recipeService = RecipeService.getInstance();

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setInitializationError(null);
      
      // Initialize services
      await Promise.all([
        advancedSettingsService.loadSettings(),
        // Add other initialization tasks here
      ]);

      // Check if LLM is configured
      const llmConfig = llmService.getConfiguration();
      const isLLMReady = !!(llmConfig && llmConfig.apiKey);
      
      setSystemReady(isLLMReady);
      setIsInitialized(true);

      // If LLM is not configured, start with dashboard to guide user through setup
      if (!isLLMReady && currentMode === 'sidebar') {
        setCurrentMode('dashboard');
      }
    } catch (error) {
      console.error('Failed to initialize ChefAI app:', error);
      setInitializationError(error.message || 'Failed to initialize application');
      setIsInitialized(true);
    }
  };

  const handleClose = () => {
    setCurrentMode('closed');
    if (onClose) {
      onClose();
    }
  };

  const handleModeSwitch = (mode: 'dashboard' | 'sidebar') => {
    setCurrentMode(mode);
  };

  const handleRecipeGenerated = async (recipe: Recipe) => {
    try {
      await recipeService.saveRecipe(recipe);
      // Could add notifications or other actions here
    } catch (error) {
      console.error('Failed to save generated recipe:', error);
    }
  };

  // Show loading state during initialization
  if (!isInitialized) {
    return (
      <div className="chefai-loading">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <h3>🍳 Initializing ChefAI...</h3>
          <p>Setting up your culinary assistant</p>
        </div>
      </div>
    );
  }

  // Show error state if initialization failed
  if (initializationError) {
    return (
      <div className="chefai-error">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h3>Initialization Failed</h3>
          <p>{initializationError}</p>
          <div className="error-actions">
            <button onClick={initializeApp} className="retry-button">
              🔄 Retry
            </button>
            <button onClick={handleClose} className="close-button">
              ✕ Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show setup guide if system is not ready
  if (!systemReady && currentMode !== 'dashboard') {
    return (
      <div className="chefai-setup">
        <div className="setup-container">
          <div className="setup-icon">🔧</div>
          <h3>Setup Required</h3>
          <p>ChefAI needs to be configured before you can generate recipes.</p>
          <div className="setup-actions">
            <button 
              onClick={() => handleModeSwitch('dashboard')} 
              className="setup-button"
            >
              🚀 Open Dashboard
            </button>
            <button onClick={handleClose} className="close-button">
              ✕ Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render the appropriate interface
  return (
    <div className="chefai-app">
      {/* Load required stylesheets */}
      <link rel="stylesheet" href={chrome.runtime.getURL('src/styles/sidebar.css')} />
      <link rel="stylesheet" href={chrome.runtime.getURL('src/styles/dashboard.css')} />
      <link rel="stylesheet" href={chrome.runtime.getURL('src/styles/customization.css')} />
      
      {currentMode === 'dashboard' && (
        <DashboardInterface
          isVisible={true}
          onClose={handleClose}
          initialTab="generator"
        />
      )}

      {currentMode === 'sidebar' && (
        <SidebarInterface
          isVisible={true}
          onClose={handleClose}
          onRecipeGenerated={handleRecipeGenerated}
          showDashboardButton={true}
        />
      )}

      {/* Mode Switch Controls (for development/testing) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="chefai-dev-controls">
          <button onClick={() => handleModeSwitch('dashboard')}>
            📊 Dashboard
          </button>
          <button onClick={() => handleModeSwitch('sidebar')}>
            📋 Sidebar
          </button>
          <button onClick={handleClose}>
            ✕ Close
          </button>
        </div>
      )}
    </div>
  );
};

// CSS for loading, error, and setup states
const appStyles = `
.chefai-loading,
.chefai-error,
.chefai-setup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10002;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.loading-container,
.error-container,
.setup-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  color: white;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.error-icon,
.setup-icon {
  font-size: 3em;
  margin-bottom: 15px;
}

.loading-container h3,
.error-container h3,
.setup-container h3 {
  margin: 0 0 10px 0;
  font-size: 1.5em;
  font-weight: 600;
}

.loading-container p,
.error-container p,
.setup-container p {
  margin: 0 0 25px 0;
  opacity: 0.9;
  line-height: 1.4;
}

.error-actions,
.setup-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.retry-button,
.setup-button,
.close-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button,
.setup-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.retry-button:hover,
.setup-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.close-button {
  background: rgba(255, 100, 100, 0.3);
  color: white;
  border: 1px solid rgba(255, 100, 100, 0.5);
}

.close-button:hover {
  background: rgba(255, 100, 100, 0.5);
  transform: translateY(-1px);
}

.chefai-dev-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 10003;
}

.chefai-dev-controls button {
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = appStyles;
  document.head.appendChild(styleElement);
}
