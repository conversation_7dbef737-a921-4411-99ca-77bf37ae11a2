// ChefAI Background Script
console.log('ChefAI Extension loaded!');

// Initialize extension
chrome.runtime.onInstalled.addListener((details) => {
  console.log('ChefAI Extension installed:', details.reason);

  // Set up initial data
  chrome.storage.local.set({
    extensionVersion: '1.0.0',
    installDate: new Date().toISOString()
  });
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  console.log('ChefAI Extension clicked on tab:', tab.url);
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);

  switch (message.type || message.action) {
    case 'GET_TAB_INFO':
      sendResponse({
        success: true,
        tabId: sender.tab?.id,
        url: sender.tab?.url
      });
      break;

    case 'GENERATE_RECIPE':
      // Mock recipe generation
      sendResponse({
        success: true,
        recipe: {
          title: 'AI Generated Recipe',
          description: 'A delicious recipe created by ChefA<PERSON>',
          ingredients: ['Sample ingredient 1', 'Sample ingredient 2'],
          instructions: ['Step 1: Prepare ingredients', 'Step 2: Cook and enjoy!']
        }
      });
      break;

    case 'openDashboard':
      // Open dashboard in new tab
      chrome.tabs.create({
        url: chrome.runtime.getURL('dashboard.html')
      });
      sendResponse({ success: true });
      break;

    default:
      sendResponse({ success: false, error: 'Unknown message type' });
  }

  return true; // Keep message channel open
});

// Handle context menu (if needed)
chrome.runtime.onStartup.addListener(() => {
  console.log('ChefAI Extension startup');
});