<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefAI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 320px;
            min-height: 400px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #2c3e50;
            color: #ecf0f1;
        }
        
        .header {
            background: #34495e;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #4a5f7a;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
            color: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .header p {
            font-size: 12px;
            color: #95a5a6;
            margin-top: 4px;
        }
        
        .content {
            padding: 20px;
        }
        
        .action-btn {
            width: 100%;
            background: #3498db;
            border: none;
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 12px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .action-btn:hover {
            background: #2980b9;
        }
        
        .action-btn.secondary {
            background: #95a5a6;
        }
        
        .action-btn.secondary:hover {
            background: #7f8c8d;
        }
        
        .status {
            background: #34495e;
            border: 1px solid #4a5f7a;
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
            font-size: 12px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
        }
        
        .status-dot.warning {
            background: #f39c12;
        }
        
        .status-dot.error {
            background: #e74c3c;
        }
        
        .shortcut {
            background: #2c3e50;
            border: 1px solid #4a5f7a;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 16px;
            text-align: center;
        }
        
        .shortcut-title {
            font-size: 12px;
            color: #95a5a6;
            margin-bottom: 4px;
        }
        
        .shortcut-key {
            font-size: 14px;
            font-weight: 600;
            color: #3498db;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            🍳 ChefAI
        </h1>
        <p>AI-Powered Recipe Generator</p>
    </div>
    
    <div class="content">
        <button class="action-btn" id="open-sidebar">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
            </svg>
            Open Recipe Generator
        </button>
        
        <button class="action-btn secondary" id="open-dashboard">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
            </svg>
            Open Dashboard
        </button>
        
        <button class="action-btn secondary" id="open-settings">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </svg>
            Settings
        </button>
        
        <div class="status">
            <div class="status-item">
                <span>Extension Status</span>
                <div class="status-dot" id="extension-status"></div>
            </div>
            <div class="status-item">
                <span>API Connection</span>
                <div class="status-dot warning" id="api-status"></div>
            </div>
        </div>
        
        <div class="shortcut">
            <div class="shortcut-title">Quick Access</div>
            <div class="shortcut-key">Ctrl + Shift + C</div>
        </div>
    </div>
    
    <script>
        // Open sidebar
        document.getElementById('open-sidebar').addEventListener('click', async () => {
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            chrome.tabs.sendMessage(tab.id, {action: 'toggleInterface'});
            window.close();
        });
        
        // Open dashboard
        document.getElementById('open-dashboard').addEventListener('click', () => {
            chrome.tabs.create({url: chrome.runtime.getURL('dashboard.html')});
            window.close();
        });
        
        // Open settings
        document.getElementById('open-settings').addEventListener('click', () => {
            chrome.runtime.openOptionsPage();
            window.close();
        });
        
        // Check status
        async function checkStatus() {
            try {
                const result = await chrome.storage.local.get(['chefai_llm_config', 'gemini_api_key']);
                const apiStatus = document.getElementById('api-status');
                
                if ((result.chefai_llm_config && result.chefai_llm_config.apiKey) || result.gemini_api_key) {
                    apiStatus.className = 'status-dot';
                } else {
                    apiStatus.className = 'status-dot error';
                }
            } catch (error) {
                console.error('Error checking status:', error);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', checkStatus);
    </script>
</body>
</html>
