import React, { useState, useEffect } from 'react';
import { AdvancedSettingsService, AdvancedSettings } from '../services/AdvancedSettingsService';

interface CustomizationPanelProps {
  onClose?: () => void;
}

export const CustomizationPanel: React.FC<CustomizationPanelProps> = ({ onClose }) => {
  const [activeSection, setActiveSection] = useState<'ai-behavior' | 'personalization' | 'interface' | 'automation'>('ai-behavior');
  const [settings, setSettings] = useState<AdvancedSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  const advancedSettingsService = AdvancedSettingsService.getInstance();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      const loadedSettings = await advancedSettingsService.loadSettings();
      setSettings(loadedSettings);
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = async (updates: Partial<AdvancedSettings>) => {
    if (!settings) return;
    
    const newSettings = { ...settings, ...updates };
    setSettings(newSettings);
    
    try {
      await advancedSettingsService.saveSettings(updates);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  const saveAllSettings = async () => {
    if (!settings) return;
    
    setIsSaving(true);
    try {
      await advancedSettingsService.saveSettings(settings);
      alert('All settings saved successfully!');
    } catch (error) {
      console.error('Failed to save all settings:', error);
      alert('Failed to save settings.');
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = async () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      try {
        await advancedSettingsService.resetToDefaults();
        await loadSettings();
        alert('Settings reset to defaults successfully!');
      } catch (error) {
        console.error('Failed to reset settings:', error);
        alert('Failed to reset settings.');
      }
    }
  };

  const exportSettings = () => {
    try {
      const exportData = advancedSettingsService.exportSettings();
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chefai-settings-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export settings:', error);
      alert('Failed to export settings.');
    }
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        const success = await advancedSettingsService.importSettings(content);
        if (success) {
          await loadSettings();
          alert('Settings imported successfully!');
        } else {
          alert('Failed to import settings. Please check the file format.');
        }
      } catch (error) {
        console.error('Failed to import settings:', error);
        alert('Failed to import settings.');
      }
    };
    reader.readAsText(file);
  };

  if (isLoading || !settings) {
    return (
      <div className="customization-panel glass-container">
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading customization settings...</p>
        </div>
      </div>
    );
  }

  const sections = [
    { id: 'ai-behavior', name: 'AI Behavior', icon: '🧠', description: 'Control how AI generates recipes' },
    { id: 'personalization', name: 'Personalization', icon: '👤', description: 'Tailor experience to your preferences' },
    { id: 'interface', name: 'Interface', icon: '🎨', description: 'Customize the user interface' },
    { id: 'automation', name: 'Automation', icon: '⚡', description: 'Set up automated features' }
  ];

  return (
    <div className="customization-panel glass-container">
      <div className="panel-header">
        <h2 className="panel-title">🔧 Advanced Customization</h2>
        {onClose && (
          <button className="panel-close" onClick={onClose}>×</button>
        )}
      </div>

      <div className="panel-navigation">
        {sections.map((section) => (
          <button
            key={section.id}
            className={`nav-section ${activeSection === section.id ? 'active' : ''}`}
            onClick={() => setActiveSection(section.id as any)}
          >
            <span className="section-icon">{section.icon}</span>
            <div className="section-info">
              <span className="section-name">{section.name}</span>
              <span className="section-description">{section.description}</span>
            </div>
          </button>
        ))}
      </div>

      <div className="panel-content">
        {activeSection === 'ai-behavior' && (
          <div className="settings-section">
            <h3>🧠 AI Behavior Settings</h3>
            
            <div className="setting-group">
              <h4>Recipe Generation</h4>
              
              <div className="setting-item">
                <label>Creativity Level ({settings.creativityLevel}%)</label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.creativityLevel}
                  onChange={(e) => updateSettings({ creativityLevel: parseInt(e.target.value) })}
                  className="glass-slider"
                />
                <div className="slider-labels">
                  <span>Conservative</span>
                  <span>Balanced</span>
                  <span>Creative</span>
                </div>
              </div>

              <div className="setting-item">
                <label>Detail Level</label>
                <select 
                  value={settings.detailLevel}
                  onChange={(e) => updateSettings({ detailLevel: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="basic">Basic - Simple instructions</option>
                  <option value="detailed">Detailed - Comprehensive guidance</option>
                  <option value="comprehensive">Comprehensive - Expert-level details</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Default Servings</label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={settings.defaultServings}
                  onChange={(e) => updateSettings({ defaultServings: parseInt(e.target.value) })}
                  className="glass-input"
                />
              </div>

              <div className="setting-item">
                <label>Ingredient Constraints</label>
                <div className="input-row">
                  <div className="input-group">
                    <label>Min Ingredients</label>
                    <input
                      type="number"
                      min="3"
                      max="15"
                      value={settings.minIngredients}
                      onChange={(e) => updateSettings({ minIngredients: parseInt(e.target.value) })}
                      className="glass-input"
                    />
                  </div>
                  <div className="input-group">
                    <label>Max Ingredients</label>
                    <input
                      type="number"
                      min="5"
                      max="30"
                      value={settings.maxIngredients}
                      onChange={(e) => updateSettings({ maxIngredients: parseInt(e.target.value) })}
                      className="glass-input"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="setting-group">
              <h4>Content Preferences</h4>
              
              <div className="checkbox-grid">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeNutritionInfo}
                    onChange={(e) => updateSettings({ includeNutritionInfo: e.target.checked })}
                  />
                  Include Nutrition Information
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeCookingTips}
                    onChange={(e) => updateSettings({ includeCookingTips: e.target.checked })}
                  />
                  Include Cooking Tips
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeSubstitutions}
                    onChange={(e) => updateSettings({ includeSubstitutions: e.target.checked })}
                  />
                  Include Ingredient Substitutions
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeVariations}
                    onChange={(e) => updateSettings({ includeVariations: e.target.checked })}
                  />
                  Include Recipe Variations
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.preferCommonIngredients}
                    onChange={(e) => updateSettings({ preferCommonIngredients: e.target.checked })}
                  />
                  Prefer Common Ingredients
                </label>
                
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.avoidRareIngredients}
                    onChange={(e) => updateSettings({ avoidRareIngredients: e.target.checked })}
                  />
                  Avoid Rare Ingredients
                </label>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'personalization' && (
          <div className="settings-section">
            <h3>👤 Personalization Settings</h3>

            <div className="setting-group">
              <h4>Language & Localization</h4>

              <div className="setting-item">
                <label>Preferred Language</label>
                <select
                  value={settings.preferredLanguage}
                  onChange={(e) => updateSettings({ preferredLanguage: e.target.value })}
                  className="glass-input"
                >
                  <option value="en">English</option>
                  <option value="ar">العربية (Arabic)</option>
                  <option value="es">Español (Spanish)</option>
                  <option value="fr">Français (French)</option>
                  <option value="de">Deutsch (German)</option>
                  <option value="it">Italiano (Italian)</option>
                  <option value="zh">中文 (Chinese)</option>
                  <option value="ja">日本語 (Japanese)</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Measurement System</label>
                <select
                  value={settings.measurementSystem}
                  onChange={(e) => updateSettings({ measurementSystem: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="metric">Metric (grams, liters, celsius)</option>
                  <option value="imperial">Imperial (ounces, cups, fahrenheit)</option>
                  <option value="mixed">Mixed (best of both)</option>
                </select>
              </div>
            </div>

            <div className="setting-group">
              <h4>Recipe Preferences</h4>

              <div className="setting-item">
                <label>Target Audience</label>
                <select
                  value={settings.targetAudience}
                  onChange={(e) => updateSettings({ targetAudience: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="family">Family (All Ages)</option>
                  <option value="adults">Adults Only</option>
                  <option value="children">Children Friendly</option>
                  <option value="elderly">Senior Friendly</option>
                  <option value="teens">Teenagers</option>
                  <option value="couples">Couples</option>
                  <option value="single">Single Person</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Cooking Skill Level</label>
                <select
                  value={settings.cookingSkillLevel}
                  onChange={(e) => updateSettings({ cookingSkillLevel: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                  <option value="professional">Professional</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Recipe Complexity</label>
                <select
                  value={settings.recipeComplexity}
                  onChange={(e) => updateSettings({ recipeComplexity: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="simple">Simple (5-10 ingredients)</option>
                  <option value="medium">Medium (10-15 ingredients)</option>
                  <option value="complex">Complex (15+ ingredients)</option>
                  <option value="gourmet">Gourmet (Professional level)</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Time Constraints</label>
                <select
                  value={settings.timeConstraints}
                  onChange={(e) => updateSettings({ timeConstraints: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="quick">Quick (Under 30 min)</option>
                  <option value="moderate">Moderate (30-60 min)</option>
                  <option value="flexible">Flexible (1-2 hours)</option>
                  <option value="weekend">Weekend Projects (2+ hours)</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Budget Range</label>
                <select
                  value={settings.budgetRange}
                  onChange={(e) => updateSettings({ budgetRange: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="budget">Budget Friendly</option>
                  <option value="medium">Medium Range</option>
                  <option value="premium">Premium Ingredients</option>
                  <option value="luxury">Luxury/Gourmet</option>
                </select>
              </div>
            </div>

            <div className="setting-group">
              <h4>Special Preferences</h4>

              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.seasonalPreferences}
                  onChange={(e) => updateSettings({ seasonalPreferences: e.target.checked })}
                />
                Prefer Seasonal Ingredients
              </label>
            </div>
          </div>
        )}

        {activeSection === 'interface' && (
          <div className="settings-section">
            <h3>🎨 Interface Customization</h3>

            <div className="setting-group">
              <h4>Display Options</h4>

              <div className="setting-item">
                <label>Recipe Style</label>
                <select
                  value={settings.recipeStyle}
                  onChange={(e) => updateSettings({ recipeStyle: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="traditional">Traditional</option>
                  <option value="modern">Modern Fusion</option>
                  <option value="comfort">Comfort Food</option>
                  <option value="healthy">Health-Focused</option>
                  <option value="gourmet">Gourmet</option>
                  <option value="quick">Quick & Easy</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Spice Level Preference</label>
                <select
                  value={settings.spiceLevel}
                  onChange={(e) => updateSettings({ spiceLevel: e.target.value as any })}
                  className="glass-input"
                >
                  <option value="mild">Mild</option>
                  <option value="medium">Medium</option>
                  <option value="hot">Hot</option>
                  <option value="very-hot">Very Hot</option>
                  <option value="variable">Variable (Ask Each Time)</option>
                </select>
              </div>
            </div>

            <div className="setting-group">
              <h4>Content Display</h4>

              <div className="checkbox-grid">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeNutritionInfo}
                    onChange={(e) => updateSettings({ includeNutritionInfo: e.target.checked })}
                  />
                  Show Nutrition Information
                </label>

                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeCookingTips}
                    onChange={(e) => updateSettings({ includeCookingTips: e.target.checked })}
                  />
                  Show Cooking Tips
                </label>

                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeSubstitutions}
                    onChange={(e) => updateSettings({ includeSubstitutions: e.target.checked })}
                  />
                  Show Ingredient Substitutions
                </label>

                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.includeVariations}
                    onChange={(e) => updateSettings({ includeVariations: e.target.checked })}
                  />
                  Show Recipe Variations
                </label>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'automation' && (
          <div className="settings-section">
            <h3>⚡ Automation Settings</h3>

            <div className="setting-group">
              <h4>Smart Suggestions</h4>

              <div className="checkbox-grid">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.preferCommonIngredients}
                    onChange={(e) => updateSettings({ preferCommonIngredients: e.target.checked })}
                  />
                  Auto-suggest Common Ingredients
                </label>

                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.avoidRareIngredients}
                    onChange={(e) => updateSettings({ avoidRareIngredients: e.target.checked })}
                  />
                  Avoid Rare/Expensive Ingredients
                </label>

                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.seasonalPreferences}
                    onChange={(e) => updateSettings({ seasonalPreferences: e.target.checked })}
                  />
                  Auto-adjust for Seasonal Ingredients
                </label>
              </div>
            </div>

            <div className="setting-group">
              <h4>Recipe Generation Automation</h4>

              <div className="setting-item">
                <label>Auto-save Generated Recipes</label>
                <select className="glass-input">
                  <option value="always">Always</option>
                  <option value="ask">Ask Each Time</option>
                  <option value="never">Never</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Auto-apply Dietary Restrictions</label>
                <select className="glass-input">
                  <option value="always">Always Apply</option>
                  <option value="suggest">Suggest Only</option>
                  <option value="manual">Manual Only</option>
                </select>
              </div>

              <div className="setting-item">
                <label>Smart Equipment Detection</label>
                <select className="glass-input">
                  <option value="enabled">Enabled</option>
                  <option value="disabled">Disabled</option>
                </select>
                <small>Automatically adjust recipes based on available kitchen equipment</small>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="panel-actions">
        <button onClick={exportSettings} className="glass-button">
          📤 Export Settings
        </button>
        
        <label className="glass-button import-button">
          📥 Import Settings
          <input
            type="file"
            accept=".json"
            onChange={importSettings}
            style={{ display: 'none' }}
          />
        </label>
        
        <button onClick={resetToDefaults} className="glass-button danger">
          🔄 Reset to Defaults
        </button>
        
        <button 
          onClick={saveAllSettings} 
          disabled={isSaving}
          className="glass-button primary"
        >
          {isSaving ? '💾 Saving...' : '💾 Save All Settings'}
        </button>
      </div>
    </div>
  );
};
