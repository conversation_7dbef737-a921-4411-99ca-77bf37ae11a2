import React, { useState, useEffect } from 'react';
import { LLMService, LLMConfig, LLMProvider } from '../services/LLMService';

interface AdvancedSettingsPanelProps {
  onClose?: () => void;
}

interface AdvancedSettings {
  targetAudience: string;
  recipeComplexity: string;
  culturalPreferences: string[];
  dietaryProfile: string[];
  cookingSkillLevel: string;
  preferredCookingMethods: string[];
  kitchenEquipment: string[];
  timeConstraints: string;
  budgetRange: string;
  seasonalPreferences: boolean;
  healthGoals: string[];
  recipeStyle: string;
  spiceLevel: string;
  mealPlanningPreferences: string[];
  nutritionFocus: string[];
}

export const AdvancedSettingsPanel: React.FC<AdvancedSettingsPanelProps> = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState<'llm' | 'preferences' | 'profile' | 'advanced'>('llm');
  
  // LLM Configuration State
  const [llmConfig, setLlmConfig] = useState<LLMConfig | null>(null);
  const [llmProviders, setLlmProviders] = useState<LLMProvider[]>([]);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<string>('');
  
  // Advanced Settings State
  const [settings, setSettings] = useState<AdvancedSettings>({
    targetAudience: 'family',
    recipeComplexity: 'medium',
    culturalPreferences: [],
    dietaryProfile: [],
    cookingSkillLevel: 'intermediate',
    preferredCookingMethods: [],
    kitchenEquipment: [],
    timeConstraints: 'flexible',
    budgetRange: 'medium',
    seasonalPreferences: true,
    healthGoals: [],
    recipeStyle: 'traditional',
    spiceLevel: 'medium',
    mealPlanningPreferences: [],
    nutritionFocus: []
  });

  const llmService = LLMService.getInstance();

  useEffect(() => {
    loadLLMConfiguration();
    loadAdvancedSettings();
  }, []);

  const loadLLMConfiguration = async () => {
    const config = llmService.getConfiguration();
    const providers = llmService.getProviders();
    setLlmConfig(config);
    setLlmProviders(providers);
  };

  const loadAdvancedSettings = async () => {
    try {
      const result = await chrome.storage.local.get(['advancedSettings']);
      if (result.advancedSettings) {
        setSettings({ ...settings, ...result.advancedSettings });
      }
    } catch (error) {
      console.error('Failed to load advanced settings:', error);
    }
  };

  const saveAdvancedSettings = async () => {
    try {
      await chrome.storage.local.set({ advancedSettings: settings });
      alert('Advanced settings saved successfully!');
    } catch (error) {
      console.error('Failed to save advanced settings:', error);
      alert('Failed to save advanced settings.');
    }
  };

  const handleLLMConfigSave = async () => {
    if (!llmConfig) return;
    
    try {
      await llmService.saveConfiguration(llmConfig);
      alert('LLM configuration saved successfully!');
    } catch (error) {
      console.error('Failed to save LLM configuration:', error);
      alert('Failed to save LLM configuration.');
    }
  };

  const handleTestConnection = async () => {
    if (!llmConfig?.apiKey) {
      setTestResult('❌ Please enter an API key first');
      return;
    }

    setIsTestingConnection(true);
    setTestResult('');
    
    try {
      const result = await llmService.testConnection();
      setTestResult(result.success ? '✅ Connection successful!' : `❌ ${result.error}`);
    } catch (error) {
      setTestResult(`❌ Connection failed: ${error.message}`);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleArrayToggle = (array: string[], setArray: (arr: string[]) => void, value: string) => {
    if (array.includes(value)) {
      setArray(array.filter(item => item !== value));
    } else {
      setArray([...array, value]);
    }
  };

  const updateSettings = (updates: Partial<AdvancedSettings>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  };

  const tabs = [
    { id: 'llm', name: 'LLM Configuration', icon: '🤖' },
    { id: 'preferences', name: 'Recipe Preferences', icon: '🍽️' },
    { id: 'profile', name: 'User Profile', icon: '👤' },
    { id: 'advanced', name: 'Advanced Options', icon: '⚙️' }
  ];

  return (
    <div className="advanced-settings-panel glass-container">
      <div className="settings-header">
        <h2 className="settings-title">🔧 Advanced Settings Dashboard</h2>
        {onClose && (
          <button className="settings-close" onClick={onClose}>×</button>
        )}
      </div>

      <div className="settings-tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`settings-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id as any)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-name">{tab.name}</span>
          </button>
        ))}
      </div>

      <div className="settings-content">
        {activeTab === 'llm' && (
          <div className="settings-section">
            <h3>🤖 LLM Configuration</h3>
            
            {llmConfig && (
              <>
                <div className="setting-item">
                  <label>AI Provider</label>
                  <select 
                    value={llmConfig.provider}
                    onChange={(e) => {
                      const newProvider = llmProviders.find(p => p.id === e.target.value);
                      setLlmConfig(prev => prev ? {
                        ...prev,
                        provider: e.target.value,
                        model: newProvider?.models[0]?.id || ''
                      } : null);
                    }}
                    className="glass-input"
                  >
                    {llmProviders.map(provider => (
                      <option key={provider.id} value={provider.id}>
                        {provider.name}
                      </option>
                    ))}
                  </select>
                  {llmProviders.find(p => p.id === llmConfig.provider) && (
                    <small className="provider-description">
                      {llmProviders.find(p => p.id === llmConfig.provider)?.description}
                    </small>
                  )}
                </div>

                <div className="setting-item">
                  <label>Model</label>
                  <select 
                    value={llmConfig.model}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, model: e.target.value } : null)}
                    className="glass-input"
                  >
                    {llmProviders.find(p => p.id === llmConfig.provider)?.models.map(model => (
                      <option key={model.id} value={model.id}>
                        {model.name} - ${model.costPer1kTokens}/1k tokens
                      </option>
                    )) || []}
                  </select>
                </div>

                <div className="setting-item">
                  <label>API Key</label>
                  <input
                    type="password"
                    value={llmConfig.apiKey}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, apiKey: e.target.value } : null)}
                    placeholder="Enter your API key..."
                    className="glass-input"
                  />
                  <small>Get your API key from the provider's website</small>
                </div>

                <div className="setting-item">
                  <label>Temperature ({llmConfig.temperature})</label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={llmConfig.temperature}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, temperature: parseFloat(e.target.value) } : null)}
                    className="glass-slider"
                  />
                  <small>Higher = more creative, Lower = more focused</small>
                </div>

                <div className="setting-item">
                  <label>Max Tokens</label>
                  <input
                    type="number"
                    min="100"
                    max="8192"
                    value={llmConfig.maxTokens}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, maxTokens: parseInt(e.target.value) } : null)}
                    className="glass-input"
                  />
                </div>

                <div className="setting-item">
                  <label>System Prompt</label>
                  <textarea
                    value={llmConfig.systemPrompt}
                    onChange={(e) => setLlmConfig(prev => prev ? { ...prev, systemPrompt: e.target.value } : null)}
                    className="glass-input"
                    rows={4}
                    style={{ resize: 'vertical', minHeight: '100px' }}
                  />
                </div>

                <div className="settings-actions">
                  <button 
                    onClick={handleTestConnection}
                    disabled={isTestingConnection || !llmConfig.apiKey}
                    className="glass-button"
                  >
                    {isTestingConnection ? 'Testing...' : 'Test Connection'}
                  </button>
                  
                  <button 
                    onClick={handleLLMConfigSave}
                    className="glass-button"
                    style={{ background: 'linear-gradient(45deg, #4CAF50, #45a049)' }}
                  >
                    Save Configuration
                  </button>
                </div>

                {testResult && (
                  <div className={`test-result ${testResult.includes('✅') ? 'success' : 'error'}`}>
                    {testResult}
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {activeTab === 'preferences' && (
          <div className="settings-section">
            <h3>🍽️ Recipe Preferences</h3>

            {/* Target Audience */}
            <div className="setting-item">
              <label>🎯 Target Audience</label>
              <select
                value={settings.targetAudience}
                onChange={(e) => updateSettings({ targetAudience: e.target.value })}
                className="glass-input"
              >
                <option value="family">Family (All Ages)</option>
                <option value="adults">Adults Only</option>
                <option value="children">Children Friendly</option>
                <option value="elderly">Senior Friendly</option>
                <option value="teens">Teenagers</option>
                <option value="couples">Couples</option>
                <option value="single">Single Person</option>
              </select>
              <small>Recipes will be tailored for your target audience</small>
            </div>

            {/* Recipe Complexity */}
            <div className="setting-item">
              <label>📊 Recipe Complexity Preference</label>
              <select
                value={settings.recipeComplexity}
                onChange={(e) => updateSettings({ recipeComplexity: e.target.value })}
                className="glass-input"
              >
                <option value="simple">Simple (5-10 ingredients)</option>
                <option value="medium">Medium (10-15 ingredients)</option>
                <option value="complex">Complex (15+ ingredients)</option>
                <option value="gourmet">Gourmet (Professional level)</option>
              </select>
            </div>

            {/* Cultural Preferences */}
            <div className="setting-item">
              <label>🌍 Cultural Preferences</label>
              <div className="checkbox-grid">
                {['Mediterranean', 'Asian', 'Middle Eastern', 'Latin American', 'European', 'African', 'Indian', 'American'].map(culture => (
                  <label key={culture} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.culturalPreferences.includes(culture)}
                      onChange={() => handleArrayToggle(settings.culturalPreferences, (arr) => updateSettings({ culturalPreferences: arr }), culture)}
                    />
                    {culture}
                  </label>
                ))}
              </div>
            </div>

            {/* Recipe Style */}
            <div className="setting-item">
              <label>🎨 Recipe Style</label>
              <select
                value={settings.recipeStyle}
                onChange={(e) => updateSettings({ recipeStyle: e.target.value })}
                className="glass-input"
              >
                <option value="traditional">Traditional</option>
                <option value="modern">Modern Fusion</option>
                <option value="comfort">Comfort Food</option>
                <option value="healthy">Health-Focused</option>
                <option value="gourmet">Gourmet</option>
                <option value="quick">Quick & Easy</option>
              </select>
            </div>

            {/* Spice Level */}
            <div className="setting-item">
              <label>🌶️ Preferred Spice Level</label>
              <select
                value={settings.spiceLevel}
                onChange={(e) => updateSettings({ spiceLevel: e.target.value })}
                className="glass-input"
              >
                <option value="mild">Mild</option>
                <option value="medium">Medium</option>
                <option value="hot">Hot</option>
                <option value="very-hot">Very Hot</option>
                <option value="variable">Variable (Ask Each Time)</option>
              </select>
            </div>

            {/* Seasonal Preferences */}
            <div className="setting-item">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={settings.seasonalPreferences}
                  onChange={(e) => updateSettings({ seasonalPreferences: e.target.checked })}
                />
                <span>🌱 Prefer Seasonal Ingredients</span>
              </label>
              <small>Prioritize ingredients that are in season</small>
            </div>
          </div>
        )}

        {activeTab === 'profile' && (
          <div className="settings-section">
            <h3>👤 User Profile</h3>

            {/* Cooking Skill Level */}
            <div className="setting-item">
              <label>👨‍🍳 Cooking Skill Level</label>
              <select
                value={settings.cookingSkillLevel}
                onChange={(e) => updateSettings({ cookingSkillLevel: e.target.value })}
                className="glass-input"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
                <option value="professional">Professional</option>
              </select>
            </div>

            {/* Dietary Profile */}
            <div className="setting-item">
              <label>🥗 Dietary Profile</label>
              <div className="checkbox-grid">
                {['Vegetarian', 'Vegan', 'Pescatarian', 'Gluten-Free', 'Dairy-Free', 'Keto', 'Paleo', 'Low-Carb', 'Mediterranean', 'DASH'].map(diet => (
                  <label key={diet} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.dietaryProfile.includes(diet)}
                      onChange={() => handleArrayToggle(settings.dietaryProfile, (arr) => updateSettings({ dietaryProfile: arr }), diet)}
                    />
                    {diet}
                  </label>
                ))}
              </div>
            </div>

            {/* Health Goals */}
            <div className="setting-item">
              <label>🏃‍♂️ Health Goals</label>
              <div className="checkbox-grid">
                {['Weight Loss', 'Muscle Building', 'Heart Healthy', 'Diabetes Friendly', 'High Protein', 'Low Sodium', 'Anti-Inflammatory', 'Energy Boosting', 'Digestive Health', 'Immune Support'].map(goal => (
                  <label key={goal} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.healthGoals.includes(goal)}
                      onChange={() => handleArrayToggle(settings.healthGoals, (arr) => updateSettings({ healthGoals: arr }), goal)}
                    />
                    {goal}
                  </label>
                ))}
              </div>
            </div>

            {/* Nutrition Focus */}
            <div className="setting-item">
              <label>📊 Nutrition Focus</label>
              <div className="checkbox-grid">
                {['High Protein', 'Low Carb', 'High Fiber', 'Low Fat', 'High Iron', 'High Calcium', 'Vitamin Rich', 'Antioxidant Rich'].map(focus => (
                  <label key={focus} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.nutritionFocus.includes(focus)}
                      onChange={() => handleArrayToggle(settings.nutritionFocus, (arr) => updateSettings({ nutritionFocus: arr }), focus)}
                    />
                    {focus}
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'advanced' && (
          <div className="settings-section">
            <h3>⚙️ Advanced Options</h3>

            {/* Preferred Cooking Methods */}
            <div className="setting-item">
              <label>🔥 Preferred Cooking Methods</label>
              <div className="checkbox-grid">
                {['Baking', 'Grilling', 'Frying', 'Steaming', 'Roasting', 'Slow Cooking', 'Pressure Cooking', 'Raw/No Cook', 'Sautéing', 'Braising'].map(method => (
                  <label key={method} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.preferredCookingMethods.includes(method)}
                      onChange={() => handleArrayToggle(settings.preferredCookingMethods, (arr) => updateSettings({ preferredCookingMethods: arr }), method)}
                    />
                    {method}
                  </label>
                ))}
              </div>
            </div>

            {/* Kitchen Equipment */}
            <div className="setting-item">
              <label>🍳 Available Kitchen Equipment</label>
              <div className="checkbox-grid">
                {['Oven', 'Stovetop', 'Microwave', 'Air Fryer', 'Slow Cooker', 'Pressure Cooker', 'Blender', 'Food Processor', 'Stand Mixer', 'Grill', 'Sous Vide', 'Dehydrator'].map(equipment => (
                  <label key={equipment} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.kitchenEquipment.includes(equipment)}
                      onChange={() => handleArrayToggle(settings.kitchenEquipment, (arr) => updateSettings({ kitchenEquipment: arr }), equipment)}
                    />
                    {equipment}
                  </label>
                ))}
              </div>
            </div>

            {/* Time Constraints */}
            <div className="setting-item">
              <label>⏰ Time Constraints</label>
              <select
                value={settings.timeConstraints}
                onChange={(e) => updateSettings({ timeConstraints: e.target.value })}
                className="glass-input"
              >
                <option value="quick">Quick (Under 30 min)</option>
                <option value="moderate">Moderate (30-60 min)</option>
                <option value="flexible">Flexible (1-2 hours)</option>
                <option value="weekend">Weekend Projects (2+ hours)</option>
              </select>
            </div>

            {/* Budget Range */}
            <div className="setting-item">
              <label>💰 Budget Range</label>
              <select
                value={settings.budgetRange}
                onChange={(e) => updateSettings({ budgetRange: e.target.value })}
                className="glass-input"
              >
                <option value="budget">Budget Friendly</option>
                <option value="medium">Medium Range</option>
                <option value="premium">Premium Ingredients</option>
                <option value="luxury">Luxury/Gourmet</option>
              </select>
            </div>

            {/* Meal Planning Preferences */}
            <div className="setting-item">
              <label>📅 Meal Planning Preferences</label>
              <div className="checkbox-grid">
                {['Batch Cooking', 'Meal Prep', 'Leftover Friendly', 'Freezer Friendly', 'One-Pot Meals', 'Make-Ahead', 'Quick Assembly'].map(pref => (
                  <label key={pref} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={settings.mealPlanningPreferences.includes(pref)}
                      onChange={() => handleArrayToggle(settings.mealPlanningPreferences, (arr) => updateSettings({ mealPlanningPreferences: arr }), pref)}
                    />
                    {pref}
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Save Button */}
        <div className="setting-item" style={{ marginTop: '30px', borderTop: '1px solid rgba(255, 255, 255, 0.2)', paddingTop: '20px' }}>
          <button
            onClick={saveAdvancedSettings}
            className="glass-button"
            style={{ background: 'linear-gradient(45deg, #4CAF50, #45a049)', width: '100%', padding: '15px' }}
          >
            💾 Save All Settings
          </button>
        </div>
      </div>
    </div>
  );
};
