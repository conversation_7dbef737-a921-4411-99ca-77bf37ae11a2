/* Options Page Styles */
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 25%, 
    #f093fb 50%, 
    #f5a9d8 75%, 
    #c4b5fd 100%
  );
  min-height: 100vh;
  color: white;
}

.options-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.options-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  margin: var(--spacing-lg);
  margin-bottom: 0;
  border-radius: var(--radius-xl);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.app-logo {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-3xl);
  box-shadow: var(--glass-shadow);
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.8;
  margin: var(--spacing-xs) 0 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.language-selector {
  min-width: 150px;
}

/* Navigation Styles */
.options-nav {
  margin: var(--spacing-lg);
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-xl);
}

.nav-tabs {
  display: flex;
  gap: var(--spacing-sm);
  overflow-x: auto;
  padding: var(--spacing-xs);
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: transparent;
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
  font-family: var(--font-family);
  font-weight: 500;
}

.nav-tab:hover {
  background: var(--glass-bg);
  color: white;
  transform: translateY(-2px);
}

.nav-tab.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.tab-icon {
  font-size: var(--font-size-lg);
}

.tab-name {
  font-size: var(--font-size-base);
}

/* Main Content Styles */
.options-main {
  flex: 1;
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

/* Dashboard Styles */
.dashboard-content .glass-card {
  margin-bottom: var(--spacing-lg);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.stat-icon {
  font-size: var(--font-size-3xl);
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* Recipes Styles */
.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.recipe-card {
  padding: var(--spacing-lg);
}

.recipe-card h3 {
  margin: 0 0 var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.recipe-card p {
  margin: 0 0 var(--spacing-md);
  opacity: 0.8;
  font-size: var(--font-size-sm);
}

.recipe-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-xs);
  opacity: 0.7;
}

/* Empty State Styles */
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

.empty-state h3 {
  font-size: var(--font-size-xl);
  margin: 0 0 var(--spacing-md);
}

.empty-state p {
  font-size: var(--font-size-base);
  opacity: 0.8;
  margin: 0 0 var(--spacing-xl);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Coming Soon Styles */
.coming-soon {
  text-align: center;
  padding: var(--spacing-2xl);
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
}

.coming-soon h3 {
  font-size: var(--font-size-xl);
  margin: 0 0 var(--spacing-md);
}

.coming-soon p {
  font-size: var(--font-size-base);
  opacity: 0.8;
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Settings Styles */
.settings-sections {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.settings-section {
  padding: var(--spacing-lg);
  background: var(--glass-bg-dark);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
}

.settings-section h3 {
  margin: 0 0 var(--spacing-lg);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--accent-color);
}

.setting-item {
  margin-bottom: var(--spacing-lg);
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  opacity: 0.9;
}

.setting-item small {
  display: block;
  font-size: var(--font-size-xs);
  opacity: 0.7;
  margin-top: var(--spacing-xs);
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.glass-slider {
  width: 100%;
  height: 6px;
  background: var(--glass-bg);
  border-radius: var(--radius-sm);
  outline: none;
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.glass-slider:hover {
  opacity: 1;
}

.glass-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
}

.glass-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .options-header,
  .options-nav,
  .options-main {
    margin: var(--spacing-sm);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .nav-tabs {
    justify-content: center;
  }

  .nav-tab {
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    min-width: 80px;
  }

  .tab-name {
    font-size: var(--font-size-xs);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .recipes-grid {
    grid-template-columns: 1fr;
  }

  .settings-sections {
    gap: var(--spacing-lg);
  }
}

/* Animations */
@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.options-main > * {
  animation: slideInUp 0.4s ease-out;
}

/* Scrollbar Styling */
.nav-tabs::-webkit-scrollbar {
  height: 6px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-sm);
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-sm);
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
