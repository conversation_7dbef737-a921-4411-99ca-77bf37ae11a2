// Real-time Updates Manager for ChefAI
class RealtimeManager {
  constructor() {
    this.listeners = new Map();
    this.isInitialized = false;
    this.syncQueue = [];
    this.lastSyncTime = Date.now();
    this.syncInterval = null;
    this.connectionStatus = 'disconnected';
    
    // Event types
    this.EVENT_TYPES = {
      SETTINGS_UPDATED: 'settings_updated',
      API_KEY_CHANGED: 'api_key_changed',
      PROVIDER_ADDED: 'provider_added',
      PROVIDER_REMOVED: 'provider_removed',
      MODEL_SETTINGS_CHANGED: 'model_settings_changed',
      THEME_CHANGED: 'theme_changed',
      RECIPE_GENERATED: 'recipe_generated',
      CONNECTION_STATUS: 'connection_status',
      USER_PREFERENCES: 'user_preferences'
    };
  }

  // Initialize the real-time manager
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Set up storage listeners
      this.setupStorageListeners();
      
      // Set up message listeners
      this.setupMessageListeners();
      
      // Start sync interval
      this.startSyncInterval();
      
      // Set connection status
      this.connectionStatus = 'connected';
      this.emit(this.EVENT_TYPES.CONNECTION_STATUS, { status: 'connected' });
      
      this.isInitialized = true;
      console.log('RealtimeManager initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize RealtimeManager:', error);
      this.connectionStatus = 'error';
      this.emit(this.EVENT_TYPES.CONNECTION_STATUS, { status: 'error', error: error.message });
    }
  }

  // Set up storage change listeners
  setupStorageListeners() {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
          this.handleStorageChanges(changes);
        }
      });
    }
  }

  // Set up message listeners for cross-tab communication
  setupMessageListeners() {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'REALTIME_SYNC') {
          this.handleRealtimeMessage(message, sender, sendResponse);
          return true; // Keep message channel open
        }
      });
    }

    // Browser storage events for same-origin communication
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', (e) => {
        if (e.key && e.key.startsWith('chefai_realtime_')) {
          this.handleBrowserStorageEvent(e);
        }
      });
    }
  }

  // Handle storage changes
  handleStorageChanges(changes) {
    for (const [key, change] of Object.entries(changes)) {
      const eventData = {
        key,
        oldValue: change.oldValue,
        newValue: change.newValue,
        timestamp: Date.now()
      };

      // Determine event type based on storage key
      let eventType = null;
      
      if (key === 'chefai_llm_config') {
        eventType = this.EVENT_TYPES.API_KEY_CHANGED;
      } else if (key === 'chefai_model_settings') {
        eventType = this.EVENT_TYPES.MODEL_SETTINGS_CHANGED;
      } else if (key === 'chefai_providers') {
        eventType = this.EVENT_TYPES.SETTINGS_UPDATED;
      } else if (key === 'chefai_theme_settings') {
        eventType = this.EVENT_TYPES.THEME_CHANGED;
      } else if (key === 'chefai_user_preferences') {
        eventType = this.EVENT_TYPES.USER_PREFERENCES;
      } else if (key.startsWith('chefai_')) {
        eventType = this.EVENT_TYPES.SETTINGS_UPDATED;
      }

      if (eventType) {
        this.emit(eventType, eventData);
        this.broadcastToAllTabs(eventType, eventData);
      }
    }
  }

  // Handle real-time messages
  handleRealtimeMessage(message, sender, sendResponse) {
    const { eventType, data, timestamp } = message;
    
    // Prevent processing old messages
    if (timestamp && (Date.now() - timestamp) > 5000) {
      return;
    }

    // Emit to local listeners
    this.emit(eventType, data);
    
    sendResponse({ success: true, processed: true });
  }

  // Handle browser storage events
  handleBrowserStorageEvent(event) {
    try {
      const data = JSON.parse(event.newValue || '{}');
      const eventType = event.key.replace('chefai_realtime_', '');
      
      this.emit(eventType, data);
    } catch (error) {
      console.error('Error parsing browser storage event:', error);
    }
  }

  // Start sync interval for periodic updates
  startSyncInterval() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.processSyncQueue();
      this.checkConnectionHealth();
    }, 1000); // Check every second
  }

  // Process queued sync operations
  processSyncQueue() {
    if (this.syncQueue.length === 0) return;

    const batch = this.syncQueue.splice(0, 10); // Process up to 10 items at once
    
    batch.forEach(item => {
      this.broadcastToAllTabs(item.eventType, item.data);
    });
  }

  // Check connection health
  checkConnectionHealth() {
    const now = Date.now();
    const timeSinceLastSync = now - this.lastSyncTime;
    
    if (timeSinceLastSync > 30000) { // 30 seconds
      this.connectionStatus = 'warning';
      this.emit(this.EVENT_TYPES.CONNECTION_STATUS, { 
        status: 'warning', 
        message: 'Slow connection detected' 
      });
    }
  }

  // Broadcast event to all tabs
  async broadcastToAllTabs(eventType, data) {
    try {
      // Chrome extension tabs
      if (typeof chrome !== 'undefined' && chrome.tabs) {
        const tabs = await chrome.tabs.query({});
        
        tabs.forEach(tab => {
          chrome.tabs.sendMessage(tab.id, {
            type: 'REALTIME_SYNC',
            eventType,
            data,
            timestamp: Date.now()
          }).catch(() => {
            // Ignore errors for tabs that don't have content script
          });
        });
      }

      // Browser storage for same-origin communication
      if (typeof localStorage !== 'undefined') {
        const storageKey = `chefai_realtime_${eventType}`;
        localStorage.setItem(storageKey, JSON.stringify({
          ...data,
          timestamp: Date.now()
        }));
        
        // Clean up after a short delay
        setTimeout(() => {
          localStorage.removeItem(storageKey);
        }, 1000);
      }

      this.lastSyncTime = Date.now();
      
    } catch (error) {
      console.error('Error broadcasting to tabs:', error);
    }
  }

  // Add event listener
  on(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    
    this.listeners.get(eventType).add(callback);
    
    // Return unsubscribe function
    return () => {
      this.off(eventType, callback);
    };
  }

  // Remove event listener
  off(eventType, callback) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).delete(callback);
    }
  }

  // Emit event to listeners
  emit(eventType, data) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${eventType}:`, error);
        }
      });
    }
  }

  // Queue sync operation
  queueSync(eventType, data) {
    this.syncQueue.push({
      eventType,
      data,
      timestamp: Date.now()
    });
  }

  // Trigger immediate sync
  async triggerSync(eventType, data) {
    this.emit(eventType, data);
    await this.broadcastToAllTabs(eventType, data);
  }

  // Get connection status
  getConnectionStatus() {
    return {
      status: this.connectionStatus,
      isInitialized: this.isInitialized,
      queueLength: this.syncQueue.length,
      lastSyncTime: this.lastSyncTime
    };
  }

  // Cleanup
  destroy() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    this.listeners.clear();
    this.syncQueue = [];
    this.isInitialized = false;
    this.connectionStatus = 'disconnected';
    
    console.log('RealtimeManager destroyed');
  }
}

// Export for use in different contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RealtimeManager;
} else if (typeof window !== 'undefined') {
  window.RealtimeManager = RealtimeManager;
}

// Create global instance
const realtimeManager = new RealtimeManager();

// Auto-initialize in appropriate contexts
if (typeof chrome !== 'undefined' || typeof window !== 'undefined') {
  realtimeManager.initialize().catch(console.error);
}
