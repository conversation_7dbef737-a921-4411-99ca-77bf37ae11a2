// Customization Panel Component for ChefAI
class CustomizationPanel {
  constructor(container) {
    this.container = container;
    this.isInitialized = false;
    this.previewMode = false;
    this.originalSettings = {};
  }

  // Initialize the customization panel
  async initialize() {
    if (this.isInitialized) return;

    try {
      await this.render();
      this.setupEventListeners();
      this.loadCurrentSettings();
      
      this.isInitialized = true;
      console.log('CustomizationPanel initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize CustomizationPanel:', error);
    }
  }

  // Render the customization panel
  async render() {
    if (!window.customizationManager) {
      console.error('CustomizationManager not available');
      return;
    }

    const options = window.customizationManager.getCustomizationOptions();
    const themes = window.customizationManager.getThemes();

    this.container.innerHTML = `
      <div class="customization-panel">
        <div class="customization-header">
          <h2>🎨 Customization</h2>
          <div class="customization-actions">
            <button class="btn-secondary" id="preview-toggle">
              <span class="preview-icon">👁️</span>
              <span class="preview-text">Preview Mode</span>
            </button>
            <button class="btn-secondary" id="reset-defaults">
              <span>🔄</span>
              Reset to Defaults
            </button>
          </div>
        </div>

        <div class="customization-content">
          <!-- Theme Selection -->
          <div class="customization-section">
            <h3>🌈 Themes</h3>
            <div class="theme-grid">
              ${themes.map(theme => `
                <div class="theme-card ${theme.id === window.customizationManager.currentTheme ? 'active' : ''}" 
                     data-theme-id="${theme.id}">
                  <div class="theme-preview" style="
                    background: ${theme.colors.background};
                    border: 2px solid ${theme.colors.border};
                  ">
                    <div class="theme-preview-header" style="background: ${theme.colors.surface};">
                      <div class="theme-preview-dot" style="background: ${theme.colors.primary};"></div>
                      <div class="theme-preview-dot" style="background: ${theme.colors.success};"></div>
                      <div class="theme-preview-dot" style="background: ${theme.colors.warning};"></div>
                    </div>
                    <div class="theme-preview-content" style="color: ${theme.colors.text};">
                      <div class="theme-preview-text" style="background: ${theme.colors.primary}; opacity: 0.8;"></div>
                      <div class="theme-preview-text" style="background: ${theme.colors.textSecondary}; opacity: 0.6;"></div>
                    </div>
                  </div>
                  <div class="theme-info">
                    <h4>${theme.name}</h4>
                    <p>${theme.description}</p>
                  </div>
                  <div class="theme-actions">
                    <button class="btn-primary theme-select" data-theme-id="${theme.id}">
                      ${theme.id === window.customizationManager.currentTheme ? 'Selected' : 'Select'}
                    </button>
                    ${!['default', 'light', 'high-contrast'].includes(theme.id) ? `
                      <button class="btn-secondary theme-edit" data-theme-id="${theme.id}">Edit</button>
                      <button class="btn-danger theme-delete" data-theme-id="${theme.id}">Delete</button>
                    ` : ''}
                  </div>
                </div>
              `).join('')}
              
              <!-- Create New Theme Card -->
              <div class="theme-card create-new">
                <div class="theme-preview create-preview">
                  <div class="create-icon">+</div>
                </div>
                <div class="theme-info">
                  <h4>Create New Theme</h4>
                  <p>Customize your own theme</p>
                </div>
                <div class="theme-actions">
                  <button class="btn-primary" id="create-theme">Create</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Customization Categories -->
          ${Object.entries(options).map(([categoryKey, category]) => `
            <div class="customization-section">
              <h3>${this.getCategoryIcon(categoryKey)} ${category.name}</h3>
              <div class="customization-options">
                ${Object.entries(category.options).map(([optionKey, option]) => `
                  <div class="customization-option">
                    <label class="option-label">
                      <span class="option-name">${option.label}</span>
                      ${this.renderOptionInput(categoryKey, optionKey, option)}
                    </label>
                  </div>
                `).join('')}
              </div>
            </div>
          `).join('')}

          <!-- Advanced Settings -->
          <div class="customization-section">
            <h3>⚙️ Advanced Settings</h3>
            <div class="customization-options">
              <div class="customization-option">
                <label class="option-label">
                  <span class="option-name">Real-time Updates</span>
                  <input type="checkbox" id="realtime-updates" checked>
                  <span class="option-description">Sync changes across all tabs instantly</span>
                </label>
              </div>
              <div class="customization-option">
                <label class="option-label">
                  <span class="option-name">Notifications</span>
                  <input type="checkbox" id="notifications-enabled" checked>
                  <span class="option-description">Show notifications for updates and changes</span>
                </label>
              </div>
              <div class="customization-option">
                <label class="option-label">
                  <span class="option-name">Auto-save</span>
                  <input type="checkbox" id="auto-save" checked>
                  <span class="option-description">Automatically save changes</span>
                </label>
              </div>
            </div>
          </div>

          <!-- Import/Export -->
          <div class="customization-section">
            <h3>📁 Import/Export</h3>
            <div class="import-export-actions">
              <button class="btn-secondary" id="export-settings">
                <span>📤</span>
                Export Settings
              </button>
              <button class="btn-secondary" id="import-settings">
                <span>📥</span>
                Import Settings
              </button>
              <input type="file" id="import-file" accept=".json" style="display: none;">
            </div>
          </div>
        </div>

        <!-- Preview Banner -->
        <div class="preview-banner" id="preview-banner" style="display: none;">
          <div class="preview-banner-content">
            <span class="preview-banner-icon">👁️</span>
            <span class="preview-banner-text">Preview Mode Active</span>
            <div class="preview-banner-actions">
              <button class="btn-primary" id="apply-preview">Apply Changes</button>
              <button class="btn-secondary" id="cancel-preview">Cancel</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // Get category icon
  getCategoryIcon(category) {
    const icons = {
      appearance: '🎨',
      behavior: '⚡',
      interface: '🖥️',
      performance: '🚀'
    };
    return icons[category] || '⚙️';
  }

  // Render option input based on type
  renderOptionInput(categoryKey, optionKey, option) {
    const inputId = `${categoryKey}-${optionKey}`;
    
    switch (option.type) {
      case 'select':
        return `
          <select id="${inputId}" class="option-select" data-category="${categoryKey}" data-option="${optionKey}">
            ${option.options.map(opt => `
              <option value="${opt.value}" ${opt.value === option.current ? 'selected' : ''}>
                ${opt.label}
              </option>
            `).join('')}
          </select>
        `;
      
      case 'color':
        return `
          <div class="color-input-wrapper">
            <input type="color" id="${inputId}" class="option-color" 
                   value="${option.current}" 
                   data-category="${categoryKey}" 
                   data-option="${optionKey}">
            <input type="text" class="color-text" value="${option.current}" readonly>
          </div>
        `;
      
      case 'range':
        return `
          <div class="range-input-wrapper">
            <input type="range" id="${inputId}" class="option-range"
                   min="${option.min}" 
                   max="${option.max}" 
                   value="${option.current}"
                   data-category="${categoryKey}" 
                   data-option="${optionKey}">
            <span class="range-value">${option.current}</span>
          </div>
        `;
      
      case 'boolean':
        return `
          <input type="checkbox" id="${inputId}" class="option-checkbox"
                 ${option.current ? 'checked' : ''}
                 data-category="${categoryKey}" 
                 data-option="${optionKey}">
        `;
      
      default:
        return `
          <input type="text" id="${inputId}" class="option-text"
                 value="${option.current}"
                 data-category="${categoryKey}" 
                 data-option="${optionKey}">
        `;
    }
  }

  // Set up event listeners
  setupEventListeners() {
    // Theme selection
    this.container.addEventListener('click', (e) => {
      if (e.target.classList.contains('theme-select')) {
        const themeId = e.target.dataset.themeId;
        this.selectTheme(themeId);
      }
    });

    // Option changes
    this.container.addEventListener('change', (e) => {
      if (e.target.dataset.category && e.target.dataset.option) {
        this.handleOptionChange(e.target);
      }
    });

    // Preview mode toggle
    const previewToggle = this.container.querySelector('#preview-toggle');
    if (previewToggle) {
      previewToggle.addEventListener('click', () => this.togglePreviewMode());
    }

    // Reset to defaults
    const resetBtn = this.container.querySelector('#reset-defaults');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => this.resetToDefaults());
    }

    // Import/Export
    const exportBtn = this.container.querySelector('#export-settings');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportSettings());
    }

    const importBtn = this.container.querySelector('#import-settings');
    if (importBtn) {
      importBtn.addEventListener('click', () => this.importSettings());
    }
  }

  // Load current settings
  async loadCurrentSettings() {
    // Settings are already loaded in the render method
    // This method can be used for additional loading if needed
  }

  // Select theme
  async selectTheme(themeId) {
    try {
      await window.customizationManager.setTheme(themeId, this.previewMode);
      
      if (!this.previewMode) {
        this.updateThemeSelection(themeId);
        
        // Show notification
        if (window.notificationManager) {
          window.notificationManager.show({
            type: 'success',
            title: 'Theme Applied',
            message: `Successfully switched to ${themeId} theme`,
            duration: 3000
          });
        }
      }
    } catch (error) {
      console.error('Error selecting theme:', error);
      
      if (window.notificationManager) {
        window.notificationManager.show({
          type: 'error',
          title: 'Theme Error',
          message: error.message,
          duration: 5000
        });
      }
    }
  }

  // Update theme selection UI
  updateThemeSelection(themeId) {
    // Remove active class from all theme cards
    this.container.querySelectorAll('.theme-card').forEach(card => {
      card.classList.remove('active');
    });

    // Add active class to selected theme
    const selectedCard = this.container.querySelector(`[data-theme-id="${themeId}"]`);
    if (selectedCard) {
      selectedCard.classList.add('active');
    }

    // Update button text
    this.container.querySelectorAll('.theme-select').forEach(btn => {
      btn.textContent = btn.dataset.themeId === themeId ? 'Selected' : 'Select';
    });
  }

  // Handle option changes
  async handleOptionChange(input) {
    const category = input.dataset.category;
    const option = input.dataset.option;
    let value = input.value;

    // Convert value based on input type
    if (input.type === 'checkbox') {
      value = input.checked;
    } else if (input.type === 'range') {
      value = parseInt(value);
      // Update range display
      const rangeValue = input.parentNode.querySelector('.range-value');
      if (rangeValue) {
        rangeValue.textContent = value;
      }
    }

    try {
      await window.customizationManager.updateCustomization(category, option, value);
      
      // Update color text input if it's a color picker
      if (input.type === 'color') {
        const colorText = input.parentNode.querySelector('.color-text');
        if (colorText) {
          colorText.value = value;
        }
      }
      
    } catch (error) {
      console.error('Error updating customization:', error);
    }
  }

  // Toggle preview mode
  togglePreviewMode() {
    this.previewMode = !this.previewMode;
    
    const previewToggle = this.container.querySelector('#preview-toggle');
    const previewBanner = this.container.querySelector('#preview-banner');
    
    if (this.previewMode) {
      previewToggle.classList.add('active');
      previewBanner.style.display = 'block';
      
      // Store original settings
      this.originalSettings = {
        theme: window.customizationManager.currentTheme,
        customizations: { ...window.customizationManager.customizations }
      };
    } else {
      previewToggle.classList.remove('active');
      previewBanner.style.display = 'none';
      
      // Restore original settings if cancelled
      if (this.originalSettings.theme) {
        window.customizationManager.setTheme(this.originalSettings.theme, false);
      }
    }
  }

  // Reset to defaults
  async resetToDefaults() {
    if (confirm('Are you sure you want to reset all customizations to defaults? This cannot be undone.')) {
      try {
        await window.customizationManager.resetToDefaults();
        
        // Refresh the panel
        await this.render();
        this.setupEventListeners();
        
        if (window.notificationManager) {
          window.notificationManager.show({
            type: 'success',
            title: 'Settings Reset',
            message: 'All customizations have been reset to defaults',
            duration: 4000
          });
        }
      } catch (error) {
        console.error('Error resetting to defaults:', error);
      }
    }
  }

  // Export settings
  exportSettings() {
    try {
      const settings = {
        theme: window.customizationManager.currentTheme,
        customizations: window.customizationManager.customizations,
        themes: window.customizationManager.getThemes().filter(t => 
          !['default', 'light', 'high-contrast'].includes(t.id)
        ),
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };

      const blob = new Blob([JSON.stringify(settings, null, 2)], { 
        type: 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chefai-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      if (window.notificationManager) {
        window.notificationManager.show({
          type: 'success',
          title: 'Settings Exported',
          message: 'Your settings have been exported successfully',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('Error exporting settings:', error);
    }
  }

  // Import settings
  importSettings() {
    const fileInput = this.container.querySelector('#import-file');
    fileInput.click();
    
    fileInput.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const settings = JSON.parse(text);
        
        // Validate settings structure
        if (!settings.theme || !settings.customizations) {
          throw new Error('Invalid settings file format');
        }

        // Import custom themes
        if (settings.themes && settings.themes.length > 0) {
          for (const theme of settings.themes) {
            window.customizationManager.themes.set(theme.id, theme);
          }
        }

        // Apply settings
        window.customizationManager.customizations = settings.customizations;
        await window.customizationManager.setTheme(settings.theme);
        await window.customizationManager.saveCustomizations();

        // Refresh the panel
        await this.render();
        this.setupEventListeners();

        if (window.notificationManager) {
          window.notificationManager.show({
            type: 'success',
            title: 'Settings Imported',
            message: 'Your settings have been imported successfully',
            duration: 4000
          });
        }
      } catch (error) {
        console.error('Error importing settings:', error);
        
        if (window.notificationManager) {
          window.notificationManager.show({
            type: 'error',
            title: 'Import Error',
            message: 'Failed to import settings: ' + error.message,
            duration: 5000
          });
        }
      }
    };
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CustomizationPanel;
} else if (typeof window !== 'undefined') {
  window.CustomizationPanel = CustomizationPanel;
}
