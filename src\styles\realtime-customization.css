/* Real-time Customization Styles for ChefAI */

/* CSS Variables for Dynamic Theming */
:root {
  --chefai-color-primary: #3498db;
  --chefai-color-secondary: #2c3e50;
  --chefai-color-background: #2c3e50;
  --chefai-color-surface: #34495e;
  --chefai-color-accent: #3498db;
  --chefai-color-text: #ecf0f1;
  --chefai-color-text-secondary: #bdc3c7;
  --chefai-color-border: #4a5f7a;
  --chefai-color-success: #27ae60;
  --chefai-color-warning: #f39c12;
  --chefai-color-error: #e74c3c;
  --chefai-color-info: #3498db;
  
  --chefai-font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --chefai-font-monospace: Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  --chefai-spacing-xs: 4px;
  --chefai-spacing-sm: 8px;
  --chefai-spacing-md: 16px;
  --chefai-spacing-lg: 24px;
  --chefai-spacing-xl: 32px;
  
  --chefai-radius-sm: 4px;
  --chefai-radius-md: 6px;
  --chefai-radius-lg: 8px;
  --chefai-radius-xl: 12px;
  
  --chefai-shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --chefai-shadow-md: 0 4px 8px rgba(0,0,0,0.15);
  --chefai-shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
  --chefai-shadow-xl: 0 12px 24px rgba(0,0,0,0.25);
}

/* Customization Panel */
.customization-panel {
  padding: var(--chefai-spacing-lg);
  background: var(--chefai-color-background);
  color: var(--chefai-color-text);
  font-family: var(--chefai-font-primary);
  min-height: 100vh;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.customization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: var(--chefai-spacing-lg);
  border-bottom: 1px solid var(--chefai-color-border);
}

.customization-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--chefai-color-text);
}

.customization-actions {
  display: flex;
  gap: var(--chefai-spacing-sm);
}

/* Button Styles */
.btn-primary {
  background: var(--chefai-color-primary);
  color: white;
  border: none;
  padding: var(--chefai-spacing-sm) var(--chefai-spacing-md);
  border-radius: var(--chefai-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--chefai-spacing-sm);
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--chefai-color-surface);
  color: var(--chefai-color-text);
  border: 1px solid var(--chefai-color-border);
  padding: var(--chefai-spacing-sm) var(--chefai-spacing-md);
  border-radius: var(--chefai-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--chefai-spacing-sm);
}

.btn-secondary:hover {
  background: #4a5f7a;
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--chefai-color-error);
  color: white;
  border: none;
  padding: var(--chefai-spacing-sm) var(--chefai-spacing-md);
  border-radius: var(--chefai-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* Theme Grid */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--chefai-spacing-lg);
  margin-bottom: 30px;
}

.theme-card {
  background: var(--chefai-color-surface);
  border: 1px solid var(--chefai-color-border);
  border-radius: var(--chefai-radius-lg);
  padding: var(--chefai-spacing-md);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.theme-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.theme-card:hover::before {
  transform: translateX(100%);
}

.theme-card:hover {
  border-color: var(--chefai-color-primary);
  box-shadow: var(--chefai-shadow-lg);
  transform: translateY(-2px);
}

.theme-card.active {
  border-color: var(--chefai-color-primary);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.theme-preview {
  width: 100%;
  height: 120px;
  border-radius: var(--chefai-radius-md);
  overflow: hidden;
  margin-bottom: var(--chefai-spacing-sm);
  position: relative;
  transition: all 0.3s ease;
}

.theme-preview-header {
  height: 30px;
  display: flex;
  align-items: center;
  padding: 0 var(--chefai-spacing-sm);
  gap: 6px;
}

.theme-preview-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.theme-card:hover .theme-preview-dot {
  transform: scale(1.2);
}

.theme-preview-content {
  padding: var(--chefai-spacing-sm);
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  gap: var(--chefai-spacing-sm);
}

.theme-preview-text {
  height: 12px;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.theme-card:hover .theme-preview-text {
  transform: translateX(2px);
}

/* Customization Options */
.customization-section {
  margin-bottom: 40px;
  opacity: 0;
  animation: fadeInUp 0.6s ease forwards;
}

.customization-section:nth-child(2) { animation-delay: 0.1s; }
.customization-section:nth-child(3) { animation-delay: 0.2s; }
.customization-section:nth-child(4) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.customization-section h3 {
  margin: 0 0 var(--chefai-spacing-lg) 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--chefai-color-text);
  display: flex;
  align-items: center;
  gap: var(--chefai-spacing-sm);
}

.customization-options {
  display: grid;
  gap: var(--chefai-spacing-lg);
}

.customization-option {
  background: var(--chefai-color-surface);
  border: 1px solid var(--chefai-color-border);
  border-radius: var(--chefai-radius-md);
  padding: var(--chefai-spacing-md);
  transition: all 0.3s ease;
}

.customization-option:hover {
  border-color: var(--chefai-color-primary);
  box-shadow: var(--chefai-shadow-sm);
}

/* Real-time Notifications */
.chefai-notification {
  position: fixed;
  top: var(--chefai-spacing-lg);
  right: var(--chefai-spacing-lg);
  background: var(--chefai-color-surface);
  border: 1px solid var(--chefai-color-border);
  border-radius: var(--chefai-radius-md);
  padding: var(--chefai-spacing-md);
  box-shadow: var(--chefai-shadow-lg);
  z-index: 10000;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.chefai-notification.show {
  transform: translateX(0);
}

.chefai-notification-success {
  border-left: 4px solid var(--chefai-color-success);
}

.chefai-notification-error {
  border-left: 4px solid var(--chefai-color-error);
}

.chefai-notification-warning {
  border-left: 4px solid var(--chefai-color-warning);
}

.chefai-notification-info {
  border-left: 4px solid var(--chefai-color-info);
}

/* Preview Mode Banner */
.preview-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--chefai-color-warning);
  color: white;
  padding: var(--chefai-spacing-sm) var(--chefai-spacing-lg);
  z-index: 10001;
  box-shadow: var(--chefai-shadow-md);
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.preview-banner.active {
  transform: translateY(0);
}

.preview-banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--chefai-spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .customization-panel {
    padding: var(--chefai-spacing-md);
  }
  
  .customization-header {
    flex-direction: column;
    gap: var(--chefai-spacing-md);
    align-items: flex-start;
  }
  
  .theme-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-banner-content {
    flex-direction: column;
    gap: var(--chefai-spacing-sm);
  }
}

/* Dark/Light Theme Transitions */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --chefai-color-border: #ffffff;
    --chefai-color-text: #ffffff;
    --chefai-shadow-sm: 0 2px 4px rgba(255,255,255,0.1);
    --chefai-shadow-md: 0 4px 8px rgba(255,255,255,0.15);
    --chefai-shadow-lg: 0 8px 16px rgba(255,255,255,0.2);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
