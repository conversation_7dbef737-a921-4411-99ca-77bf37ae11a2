import { useState, useEffect, useCallback } from 'react';
import { translations } from '../utils/translations';

type TranslationKey = keyof typeof translations.en;

export const useI18n = () => {
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');
  const [isLoading, setIsLoading] = useState(true);

  // Load saved language preference
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const result = await chrome.storage.sync.get(['language']);
        if (result.language) {
          setCurrentLanguage(result.language);
        } else {
          // Detect browser language
          const browserLang = navigator.language.split('-')[0];
          if (translations[browserLang as keyof typeof translations]) {
            setCurrentLanguage(browserLang);
          }
        }
      } catch (error) {
        console.error('Failed to load language preference:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguage();
  }, []);

  // Change language
  const changeLanguage = useCallback(async (language: string) => {
    try {
      await chrome.storage.sync.set({ language });
      setCurrentLanguage(language);
    } catch (error) {
      console.error('Failed to save language preference:', error);
    }
  }, []);

  // Translation function
  const t = useCallback((key: string, params?: Record<string, any>): string => {
    const langTranslations = translations[currentLanguage as keyof typeof translations] || translations.en;
    
    // Navigate through nested keys (e.g., 'app.title')
    const keys = key.split('.');
    let value: any = langTranslations;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // Fallback to English if key not found
        const enTranslations = translations.en;
        let fallbackValue: any = enTranslations;
        for (const fk of keys) {
          if (fallbackValue && typeof fallbackValue === 'object' && fk in fallbackValue) {
            fallbackValue = fallbackValue[fk];
          } else {
            return key; // Return key if not found in fallback
          }
        }
        value = fallbackValue;
        break;
      }
    }

    if (typeof value !== 'string') {
      return key;
    }

    // Replace parameters in translation
    if (params) {
      return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
        return params[paramKey] !== undefined ? String(params[paramKey]) : match;
      });
    }

    return value;
  }, [currentLanguage]);

  // Get available languages
  const getAvailableLanguages = useCallback(() => {
    return Object.keys(translations).map(code => ({
      code,
      name: translations[code as keyof typeof translations].language_name,
      nativeName: translations[code as keyof typeof translations].language_native_name
    }));
  }, []);

  // Check if language is RTL
  const isRTL = useCallback(() => {
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    return rtlLanguages.includes(currentLanguage);
  }, [currentLanguage]);

  return {
    t,
    currentLanguage,
    changeLanguage,
    getAvailableLanguages,
    isRTL,
    isLoading
  };
};
