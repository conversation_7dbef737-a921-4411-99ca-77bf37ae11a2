/* Content Script Styles */

#chefai-fab {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  width: 56px !important;
  height: 56px !important;
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 24px !important;
  cursor: pointer !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  z-index: 10000 !important;
  transition: transform 0.2s ease !important;
  border: none !important;
  outline: none !important;
}

#chefai-fab:hover {
  transform: scale(1.1) !important;
}

/* Recipe highlighting styles */
.chefai-highlight {
  background-color: #fbbf24 !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Recipe extraction overlay */
.chefai-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  z-index: 9999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.chefai-modal {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  padding: 24px !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  color: white !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}
