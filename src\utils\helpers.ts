import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a unique ID
 */
export const generateUniqueId = (): string => {
  return uuidv4();
};

/**
 * Format cooking time in a human-readable format
 */
export const formatCookingTime = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} hr`;
  }
  
  return `${hours} hr ${remainingMinutes} min`;
};

/**
 * Format serving size
 */
export const formatServings = (servings: number): string => {
  return servings === 1 ? '1 serving' : `${servings} servings`;
};

/**
 * Capitalize first letter of each word
 */
export const capitalizeWords = (str: string): string => {
  return str.replace(/\b\w/g, (char) => char.toUpperCase());
};

/**
 * Truncate text to specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Format ingredient amount and unit
 */
export const formatIngredientAmount = (amount: number, unit: string): string => {
  // Convert decimal amounts to fractions for common cooking measurements
  const fractions: { [key: number]: string } = {
    0.125: '1/8',
    0.25: '1/4',
    0.33: '1/3',
    0.5: '1/2',
    0.67: '2/3',
    0.75: '3/4'
  };

  const decimalPart = amount % 1;
  const wholePart = Math.floor(amount);

  let formattedAmount = '';
  
  if (wholePart > 0) {
    formattedAmount += wholePart.toString();
  }
  
  if (decimalPart > 0) {
    const fraction = fractions[Math.round(decimalPart * 100) / 100];
    if (fraction) {
      formattedAmount += (wholePart > 0 ? ' ' : '') + fraction;
    } else {
      formattedAmount += (wholePart > 0 ? '.' : '0.') + decimalPart.toString().substring(2);
    }
  }

  return `${formattedAmount} ${unit}${amount !== 1 && !unit.endsWith('s') ? 's' : ''}`;
};

/**
 * Calculate total cooking time
 */
export const calculateTotalTime = (prepTime: number, cookTime: number): number => {
  return prepTime + cookTime;
};

/**
 * Get difficulty color
 */
export const getDifficultyColor = (difficulty: string): string => {
  switch (difficulty.toLowerCase()) {
    case 'easy':
      return '#4ade80'; // green
    case 'medium':
      return '#fbbf24'; // yellow
    case 'hard':
      return '#f87171'; // red
    default:
      return '#6b7280'; // gray
  }
};

/**
 * Get cuisine flag emoji
 */
export const getCuisineFlag = (cuisine: string): string => {
  const flags: { [key: string]: string } = {
    'italian': '🇮🇹',
    'chinese': '🇨🇳',
    'mexican': '🇲🇽',
    'indian': '🇮🇳',
    'french': '🇫🇷',
    'japanese': '🇯🇵',
    'thai': '🇹🇭',
    'american': '🇺🇸',
    'mediterranean': '🇬🇷',
    'middle eastern': '🇱🇧',
    'korean': '🇰🇷',
    'spanish': '🇪🇸',
    'german': '🇩🇪',
    'british': '🇬🇧'
  };

  return flags[cuisine.toLowerCase()] || '🌍';
};

/**
 * Format nutritional value
 */
export const formatNutritionalValue = (value: number, unit: string): string => {
  if (value < 1) {
    return `${(value * 1000).toFixed(0)}m${unit}`;
  }
  return `${value.toFixed(1)}${unit}`;
};

/**
 * Generate recipe URL slug
 */
export const generateRecipeSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

/**
 * Check if ingredient is common allergen
 */
export const isCommonAllergen = (ingredient: string): boolean => {
  const allergens = [
    'milk', 'dairy', 'cheese', 'butter', 'cream',
    'egg', 'eggs',
    'wheat', 'flour', 'gluten',
    'soy', 'soybean',
    'peanut', 'peanuts',
    'tree nuts', 'almonds', 'walnuts', 'cashews',
    'fish', 'salmon', 'tuna',
    'shellfish', 'shrimp', 'crab', 'lobster',
    'sesame'
  ];

  return allergens.some(allergen => 
    ingredient.toLowerCase().includes(allergen)
  );
};

/**
 * Convert temperature between Celsius and Fahrenheit
 */
export const convertTemperature = (temp: number, fromUnit: 'C' | 'F', toUnit: 'C' | 'F'): number => {
  if (fromUnit === toUnit) return temp;
  
  if (fromUnit === 'C' && toUnit === 'F') {
    return (temp * 9/5) + 32;
  } else {
    return (temp - 32) * 5/9;
  }
};

/**
 * Format temperature with unit
 */
export const formatTemperature = (temp: number, unit: 'C' | 'F' = 'C'): string => {
  return `${Math.round(temp)}°${unit}`;
};

/**
 * Debounce function for search inputs
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Deep clone an object
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  const cloned = {} as T;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
};

/**
 * Check if two arrays are equal
 */
export const arraysEqual = <T>(a: T[], b: T[]): boolean => {
  if (a.length !== b.length) return false;
  return a.every((val, index) => val === b[index]);
};

/**
 * Remove duplicates from array
 */
export const removeDuplicates = <T>(array: T[]): T[] => {
  return [...new Set(array)];
};

/**
 * Shuffle array randomly
 */
export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Format file size in human readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Generate random color
 */
export const generateRandomColor = (): string => {
  const colors = [
    '#667eea', '#764ba2', '#f093fb', '#4ade80',
    '#fbbf24', '#f87171', '#8b5cf6', '#06b6d4',
    '#10b981', '#f59e0b', '#ef4444', '#3b82f6'
  ];
  
  return colors[Math.floor(Math.random() * colors.length)];
};
