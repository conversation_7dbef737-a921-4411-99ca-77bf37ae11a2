// Realtime Update Service - Manages live updates and synchronization
export interface UpdateEvent {
  type: 'recipe_generated' | 'settings_changed' | 'llm_configured' | 'ingredient_updated' | 'custom_event';
  data: any;
  timestamp: number;
  source: string;
}

export interface UpdateListener {
  id: string;
  eventTypes: string[];
  callback: (event: UpdateEvent) => void;
  priority: number;
}

export interface RealtimeConfig {
  enableBroadcast: boolean;
  enablePersistence: boolean;
  maxEventHistory: number;
  debounceDelay: number;
  batchUpdates: boolean;
  batchSize: number;
  batchDelay: number;
}

export class RealtimeUpdateService {
  private static instance: RealtimeUpdateService;
  private listeners: Map<string, UpdateListener> = new Map();
  private eventHistory: UpdateEvent[] = [];
  private pendingUpdates: UpdateEvent[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  
  private config: RealtimeConfig = {
    enableBroadcast: true,
    enablePersistence: true,
    maxEventHistory: 100,
    debounceDelay: 300,
    batchUpdates: true,
    batchSize: 10,
    batchDelay: 100
  };

  private constructor() {
    this.setupStorageListener();
    this.setupVisibilityListener();
  }

  public static getInstance(): RealtimeUpdateService {
    if (!RealtimeUpdateService.instance) {
      RealtimeUpdateService.instance = new RealtimeUpdateService();
    }
    return RealtimeUpdateService.instance;
  }

  public configure(config: Partial<RealtimeConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public subscribe(
    id: string,
    eventTypes: string[],
    callback: (event: UpdateEvent) => void,
    priority: number = 0
  ): void {
    const listener: UpdateListener = {
      id,
      eventTypes,
      callback,
      priority
    };

    this.listeners.set(id, listener);
    
    // Sort listeners by priority
    this.sortListeners();
  }

  public unsubscribe(id: string): void {
    this.listeners.delete(id);
  }

  public emit(type: string, data: any, source: string = 'unknown'): void {
    const event: UpdateEvent = {
      type: type as any,
      data,
      timestamp: Date.now(),
      source
    };

    // Add to history
    this.addToHistory(event);

    // Handle debouncing
    if (this.config.debounceDelay > 0) {
      this.debounceEmit(event);
    } else {
      this.processEvent(event);
    }
  }

  private debounceEmit(event: UpdateEvent): void {
    const key = `${event.type}_${event.source}`;
    
    // Clear existing timer
    if (this.debounceTimers.has(key)) {
      clearTimeout(this.debounceTimers.get(key)!);
    }

    // Set new timer
    const timer = setTimeout(() => {
      this.processEvent(event);
      this.debounceTimers.delete(key);
    }, this.config.debounceDelay);

    this.debounceTimers.set(key, timer);
  }

  private processEvent(event: UpdateEvent): void {
    if (this.config.batchUpdates) {
      this.addToBatch(event);
    } else {
      this.notifyListeners([event]);
    }

    // Broadcast to other tabs/windows
    if (this.config.enableBroadcast) {
      this.broadcastEvent(event);
    }

    // Persist if enabled
    if (this.config.enablePersistence) {
      this.persistEvent(event);
    }
  }

  private addToBatch(event: UpdateEvent): void {
    this.pendingUpdates.push(event);

    // Process batch if size limit reached
    if (this.pendingUpdates.length >= this.config.batchSize) {
      this.processBatch();
    } else if (!this.batchTimer) {
      // Set timer for batch processing
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, this.config.batchDelay);
    }
  }

  private processBatch(): void {
    if (this.pendingUpdates.length === 0) return;

    const batch = [...this.pendingUpdates];
    this.pendingUpdates = [];

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    this.notifyListeners(batch);
  }

  private notifyListeners(events: UpdateEvent[]): void {
    // Get sorted listeners
    const sortedListeners = Array.from(this.listeners.values())
      .sort((a, b) => b.priority - a.priority);

    for (const listener of sortedListeners) {
      for (const event of events) {
        if (listener.eventTypes.includes(event.type) || listener.eventTypes.includes('*')) {
          try {
            listener.callback(event);
          } catch (error) {
            console.error(`Error in listener ${listener.id}:`, error);
          }
        }
      }
    }
  }

  private sortListeners(): void {
    // Listeners are sorted when retrieved, no need to maintain sorted order
  }

  private addToHistory(event: UpdateEvent): void {
    this.eventHistory.push(event);
    
    // Trim history if needed
    if (this.eventHistory.length > this.config.maxEventHistory) {
      this.eventHistory = this.eventHistory.slice(-this.config.maxEventHistory);
    }
  }

  private broadcastEvent(event: UpdateEvent): void {
    try {
      // Use chrome.storage for cross-tab communication
      const broadcastData = {
        chefai_broadcast: true,
        event,
        timestamp: Date.now()
      };
      
      chrome.storage.local.set({ chefai_latest_event: broadcastData });
    } catch (error) {
      console.error('Failed to broadcast event:', error);
    }
  }

  private persistEvent(event: UpdateEvent): void {
    try {
      // Store recent events for persistence across sessions
      chrome.storage.local.get(['chefai_event_history'], (result) => {
        const history = result.chefai_event_history || [];
        history.push(event);
        
        // Keep only recent events
        const recentHistory = history.slice(-50);
        
        chrome.storage.local.set({ chefai_event_history: recentHistory });
      });
    } catch (error) {
      console.error('Failed to persist event:', error);
    }
  }

  private setupStorageListener(): void {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local' && changes.chefai_latest_event) {
          const broadcastData = changes.chefai_latest_event.newValue;
          
          if (broadcastData && broadcastData.chefai_broadcast) {
            // Don't process our own broadcasts
            if (broadcastData.timestamp !== Date.now()) {
              this.handleBroadcastEvent(broadcastData.event);
            }
          }
        }
      });
    }
  }

  private setupVisibilityListener(): void {
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          // Tab became visible, sync with latest state
          this.syncOnVisibilityChange();
        }
      });
    }
  }

  private handleBroadcastEvent(event: UpdateEvent): void {
    // Add to history but don't re-broadcast
    this.addToHistory(event);
    
    // Notify local listeners
    this.notifyListeners([event]);
  }

  private async syncOnVisibilityChange(): Promise<void> {
    try {
      // Load recent events from storage
      const result = await chrome.storage.local.get(['chefai_event_history']);
      const storedHistory = result.chefai_event_history || [];
      
      // Find events that occurred while tab was hidden
      const lastLocalTimestamp = this.eventHistory.length > 0 
        ? this.eventHistory[this.eventHistory.length - 1].timestamp 
        : 0;
      
      const missedEvents = storedHistory.filter(
        (event: UpdateEvent) => event.timestamp > lastLocalTimestamp
      );
      
      // Process missed events
      for (const event of missedEvents) {
        this.handleBroadcastEvent(event);
      }
    } catch (error) {
      console.error('Failed to sync on visibility change:', error);
    }
  }

  public getEventHistory(eventType?: string, limit?: number): UpdateEvent[] {
    let history = this.eventHistory;
    
    if (eventType) {
      history = history.filter(event => event.type === eventType);
    }
    
    if (limit) {
      history = history.slice(-limit);
    }
    
    return [...history];
  }

  public clearHistory(): void {
    this.eventHistory = [];
    
    if (this.config.enablePersistence) {
      chrome.storage.local.remove(['chefai_event_history']);
    }
  }

  public getListenerCount(): number {
    return this.listeners.size;
  }

  public getConfig(): RealtimeConfig {
    return { ...this.config };
  }

  public destroy(): void {
    // Clear all timers
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
    
    // Clear listeners
    this.listeners.clear();
    
    // Clear pending updates
    this.pendingUpdates = [];
  }

  // Utility methods for common events
  public emitRecipeGenerated(recipe: any): void {
    this.emit('recipe_generated', recipe, 'recipe_generator');
  }

  public emitSettingsChanged(settings: any): void {
    this.emit('settings_changed', settings, 'settings_panel');
  }

  public emitLLMConfigured(config: any): void {
    this.emit('llm_configured', config, 'llm_service');
  }

  public emitIngredientUpdated(ingredient: any): void {
    this.emit('ingredient_updated', ingredient, 'ingredient_manager');
  }
}
