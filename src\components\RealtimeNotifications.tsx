import React, { useState, useEffect } from 'react';
import { RealtimeUpdateService, UpdateEvent } from '../services/RealtimeUpdateService';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: number;
  duration: number;
  actions?: Array<{
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary';
  }>;
  persistent?: boolean;
}

interface RealtimeNotificationsProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  maxNotifications?: number;
  defaultDuration?: number;
}

export const RealtimeNotifications: React.FC<RealtimeNotificationsProps> = ({
  position = 'top-right',
  maxNotifications = 5,
  defaultDuration = 5000
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isVisible, setIsVisible] = useState(true);
  const realtimeService = RealtimeUpdateService.getInstance();

  useEffect(() => {
    // Subscribe to realtime updates
    realtimeService.subscribe(
      'notifications',
      ['*'], // Listen to all events
      handleRealtimeEvent,
      5
    );

    return () => {
      realtimeService.unsubscribe('notifications');
    };
  }, []);

  const handleRealtimeEvent = (event: UpdateEvent) => {
    const notification = createNotificationFromEvent(event);
    if (notification) {
      addNotification(notification);
    }
  };

  const createNotificationFromEvent = (event: UpdateEvent): Notification | null => {
    switch (event.type) {
      case 'recipe_generated':
        return {
          id: `recipe_${event.timestamp}`,
          type: 'success',
          title: '🍳 Recipe Generated!',
          message: `New recipe "${event.data.title}" has been created successfully.`,
          timestamp: event.timestamp,
          duration: defaultDuration,
          actions: [
            {
              label: 'View Recipe',
              action: () => viewRecipe(event.data.id),
              style: 'primary'
            },
            {
              label: 'Save',
              action: () => saveRecipe(event.data.id),
              style: 'secondary'
            }
          ]
        };

      case 'settings_changed':
        return {
          id: `settings_${event.timestamp}`,
          type: 'info',
          title: '⚙️ Settings Updated',
          message: 'Your preferences have been saved successfully.',
          timestamp: event.timestamp,
          duration: 3000
        };

      case 'llm_configured':
        return {
          id: `llm_${event.timestamp}`,
          type: 'success',
          title: '🤖 AI Model Configured',
          message: `${event.data.provider} model is now ready for recipe generation.`,
          timestamp: event.timestamp,
          duration: defaultDuration
        };

      case 'ingredient_updated':
        return {
          id: `ingredient_${event.timestamp}`,
          type: 'info',
          title: '🥕 Ingredient Updated',
          message: `Ingredient "${event.data.name}" has been updated in your pantry.`,
          timestamp: event.timestamp,
          duration: 2000
        };

      default:
        return null;
    }
  };

  const addNotification = (notification: Notification) => {
    setNotifications(prev => {
      const updated = [notification, ...prev];
      
      // Limit number of notifications
      if (updated.length > maxNotifications) {
        return updated.slice(0, maxNotifications);
      }
      
      return updated;
    });

    // Auto-remove notification after duration
    if (!notification.persistent && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(notification.id);
      }, notification.duration);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const viewRecipe = (recipeId: string) => {
    // Emit event to show recipe
    realtimeService.emit('show_recipe', { recipeId }, 'notifications');
  };

  const saveRecipe = (recipeId: string) => {
    // Emit event to save recipe
    realtimeService.emit('save_recipe', { recipeId }, 'notifications');
  };

  const getPositionClasses = () => {
    const baseClasses = 'realtime-notifications';
    switch (position) {
      case 'top-left':
        return `${baseClasses} top-left`;
      case 'bottom-right':
        return `${baseClasses} bottom-right`;
      case 'bottom-left':
        return `${baseClasses} bottom-left`;
      default:
        return `${baseClasses} top-right`;
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  if (!isVisible || notifications.length === 0) {
    return null;
  }

  return (
    <div className={getPositionClasses()}>
      <div className="notifications-header">
        <span className="notifications-title">Notifications</span>
        <div className="notifications-controls">
          <button
            className="control-button"
            onClick={() => setIsVisible(false)}
            title="Hide notifications"
          >
            👁️
          </button>
          <button
            className="control-button"
            onClick={clearAllNotifications}
            title="Clear all"
          >
            🗑️
          </button>
        </div>
      </div>

      <div className="notifications-list">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`notification notification-${notification.type}`}
          >
            <div className="notification-header">
              <span className="notification-icon">
                {getNotificationIcon(notification.type)}
              </span>
              <span className="notification-title">{notification.title}</span>
              <button
                className="notification-close"
                onClick={() => removeNotification(notification.id)}
              >
                ×
              </button>
            </div>

            <div className="notification-content">
              <p className="notification-message">{notification.message}</p>
              
              {notification.actions && notification.actions.length > 0 && (
                <div className="notification-actions">
                  {notification.actions.map((action, index) => (
                    <button
                      key={index}
                      className={`notification-action ${action.style || 'secondary'}`}
                      onClick={() => {
                        action.action();
                        if (!notification.persistent) {
                          removeNotification(notification.id);
                        }
                      }}
                    >
                      {action.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            <div className="notification-timestamp">
              {new Date(notification.timestamp).toLocaleTimeString()}
            </div>
          </div>
        ))}
      </div>

      {!isVisible && (
        <button
          className="show-notifications-button"
          onClick={() => setIsVisible(true)}
          title={`Show ${notifications.length} notifications`}
        >
          🔔 {notifications.length}
        </button>
      )}
    </div>
  );
};

// CSS styles for notifications
const notificationStyles = `
.realtime-notifications {
  position: fixed;
  z-index: 10005;
  width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.realtime-notifications.top-right {
  top: 20px;
  right: 20px;
}

.realtime-notifications.top-left {
  top: 20px;
  left: 20px;
}

.realtime-notifications.bottom-right {
  bottom: 20px;
  right: 20px;
}

.realtime-notifications.bottom-left {
  bottom: 20px;
  left: 20px;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px 12px 0 0;
  color: white;
  font-weight: 600;
  margin-bottom: 8px;
}

.notifications-controls {
  display: flex;
  gap: 8px;
}

.control-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  font-size: 12px;
  transition: background 0.2s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notification {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  border-left: 4px solid;
}

.notification-success {
  border-left-color: #4CAF50;
}

.notification-error {
  border-left-color: #f44336;
}

.notification-warning {
  border-left-color: #ff9800;
}

.notification-info {
  border-left-color: #2196F3;
}

.notification-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px 8px;
  background: rgba(0, 0, 0, 0.05);
}

.notification-icon {
  font-size: 16px;
}

.notification-title {
  flex: 1;
  font-weight: 600;
  color: #333;
}

.notification-close {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.notification-content {
  padding: 0 16px 12px;
}

.notification-message {
  margin: 0 0 12px 0;
  color: #555;
  line-height: 1.4;
  font-size: 14px;
}

.notification-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.notification-action {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-action.primary {
  background: #667eea;
  color: white;
}

.notification-action.primary:hover {
  background: #5a6fd8;
}

.notification-action.secondary {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.notification-action.secondary:hover {
  background: rgba(0, 0, 0, 0.2);
}

.notification-timestamp {
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.05);
  font-size: 11px;
  color: #666;
  text-align: right;
}

.show-notifications-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 600;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.show-notifications-button:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.05);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Scrollbar styling */
.realtime-notifications::-webkit-scrollbar {
  width: 6px;
}

.realtime-notifications::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.realtime-notifications::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.realtime-notifications::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = notificationStyles;
  document.head.appendChild(styleElement);
}
