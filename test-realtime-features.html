<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefAI Real-time Features Test</title>
    <link rel="stylesheet" href="src/styles/realtime-customization.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--chefai-color-background, #2c3e50);
            color: var(--chefai-color-text, #ecf0f1);
            font-family: var(--chefai-font-primary, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: var(--chefai-color-surface, #34495e);
            border-radius: var(--chefai-radius-lg, 8px);
            border: 1px solid var(--chefai-color-border, #4a5f7a);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--chefai-color-surface, #34495e);
            border-radius: var(--chefai-radius-md, 6px);
            border: 1px solid var(--chefai-color-border, #4a5f7a);
        }
        
        .test-section h3 {
            margin-top: 0;
            color: var(--chefai-color-primary, #3498db);
        }
        
        .test-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin: 16px 0;
        }
        
        .test-btn {
            background: var(--chefai-color-primary, #3498db);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--chefai-radius-sm, 4px);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }
        
        .test-btn.secondary {
            background: var(--chefai-color-surface, #34495e);
            border: 1px solid var(--chefai-color-border, #4a5f7a);
        }
        
        .test-btn.danger {
            background: var(--chefai-color-error, #e74c3c);
        }
        
        .status-display {
            background: var(--chefai-color-background, #2c3e50);
            border: 1px solid var(--chefai-color-border, #4a5f7a);
            border-radius: var(--chefai-radius-sm, 4px);
            padding: 12px;
            margin: 12px 0;
            font-family: var(--chefai-font-monospace, monospace);
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .connection-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .connection-status.connected {
            background: var(--chefai-color-success, #27ae60);
        }
        
        .connection-status.disconnected {
            background: var(--chefai-color-error, #e74c3c);
        }
        
        .connection-status.warning {
            background: var(--chefai-color-warning, #f39c12);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 ChefAI Real-time Features Test</h1>
            <p>Test and demonstrate the real-time updates, customization, and notification features</p>
            <div>
                <span class="connection-status" id="connection-indicator"></span>
                <span id="connection-text">Initializing...</span>
            </div>
        </div>

        <!-- Real-time Manager Test -->
        <div class="test-section">
            <h3>⚡ Real-time Manager</h3>
            <p>Test real-time synchronization across tabs and windows</p>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="testRealtimeSync()">Test Sync</button>
                <button class="test-btn" onclick="triggerSettingsUpdate()">Trigger Settings Update</button>
                <button class="test-btn" onclick="triggerAPIKeyChange()">Trigger API Key Change</button>
                <button class="test-btn secondary" onclick="checkConnectionStatus()">Check Status</button>
            </div>
            
            <div class="status-display" id="realtime-status">
                Real-time manager status will appear here...
            </div>
        </div>

        <!-- Customization Manager Test -->
        <div class="test-section">
            <h3>🎨 Customization Manager</h3>
            <p>Test theme switching and customization features</p>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="switchTheme('default')">Default Theme</button>
                <button class="test-btn" onclick="switchTheme('light')">Light Theme</button>
                <button class="test-btn" onclick="switchTheme('high-contrast')">High Contrast</button>
                <button class="test-btn secondary" onclick="openCustomizationPanel()">Open Customization</button>
                <button class="test-btn danger" onclick="resetCustomizations()">Reset All</button>
            </div>
            
            <div class="status-display" id="customization-status">
                Customization status will appear here...
            </div>
        </div>

        <!-- Notification Manager Test -->
        <div class="test-section">
            <h3>🔔 Notification Manager</h3>
            <p>Test smart notifications and real-time alerts</p>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="showSuccessNotification()">Success</button>
                <button class="test-btn" onclick="showErrorNotification()">Error</button>
                <button class="test-btn" onclick="showWarningNotification()">Warning</button>
                <button class="test-btn" onclick="showRecipeNotification()">Recipe Generated</button>
                <button class="test-btn secondary" onclick="clearAllNotifications()">Clear All</button>
            </div>
            
            <div class="status-display" id="notification-status">
                Notification status will appear here...
            </div>
        </div>

        <!-- Live Demo Section -->
        <div class="test-section">
            <h3>🎯 Live Demo</h3>
            <p>Simulate real-world usage scenarios</p>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="simulateRecipeGeneration()">Simulate Recipe Generation</button>
                <button class="test-btn" onclick="simulateSettingsSync()">Simulate Settings Sync</button>
                <button class="test-btn" onclick="simulateThemeChange()">Simulate Theme Change</button>
                <button class="test-btn secondary" onclick="openNewTab()">Open New Tab</button>
            </div>
            
            <div class="status-display" id="demo-status">
                Demo status will appear here...
            </div>
        </div>

        <!-- Customization Panel Container -->
        <div id="customization-container" style="display: none;"></div>
    </div>

    <!-- Load Services -->
    <script src="src/services/realtime-manager.js"></script>
    <script src="src/services/customization-manager.js"></script>
    <script src="src/services/notification-manager.js"></script>
    <script src="src/components/customization-panel.js"></script>

    <script>
        // Initialize test environment
        let testLog = [];
        
        async function initializeTest() {
            try {
                // Initialize all services
                if (window.realtimeManager) {
                    await window.realtimeManager.initialize();
                    updateConnectionStatus('connected');
                    logStatus('realtime-status', 'Real-time manager initialized successfully');
                }
                
                if (window.customizationManager) {
                    await window.customizationManager.initialize();
                    logStatus('customization-status', 'Customization manager initialized successfully');
                }
                
                if (window.notificationManager) {
                    await window.notificationManager.initialize();
                    logStatus('notification-status', 'Notification manager initialized successfully');
                }
                
                // Set up event listeners
                setupEventListeners();
                
                console.log('Test environment initialized successfully');
                
            } catch (error) {
                console.error('Error initializing test environment:', error);
                updateConnectionStatus('disconnected');
            }
        }
        
        function setupEventListeners() {
            if (window.realtimeManager) {
                window.realtimeManager.on('settings_updated', (data) => {
                    logStatus('realtime-status', `Settings updated: ${JSON.stringify(data)}`);
                });
                
                window.realtimeManager.on('api_key_changed', (data) => {
                    logStatus('realtime-status', `API key changed: ${JSON.stringify(data)}`);
                });
                
                window.realtimeManager.on('theme_changed', (data) => {
                    logStatus('realtime-status', `Theme changed: ${JSON.stringify(data)}`);
                });
                
                window.realtimeManager.on('connection_status', (data) => {
                    updateConnectionStatus(data.status);
                    logStatus('realtime-status', `Connection status: ${data.status}`);
                });
            }
        }
        
        function updateConnectionStatus(status) {
            const indicator = document.getElementById('connection-indicator');
            const text = document.getElementById('connection-text');
            
            indicator.className = `connection-status ${status}`;
            text.textContent = `Connection: ${status}`;
        }
        
        function logStatus(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            element.innerHTML += logEntry + '\n';
            element.scrollTop = element.scrollHeight;
            
            testLog.push(logEntry);
        }
        
        // Real-time Manager Tests
        function testRealtimeSync() {
            if (window.realtimeManager) {
                window.realtimeManager.triggerSync('test_event', {
                    message: 'Test sync message',
                    timestamp: Date.now()
                });
                logStatus('realtime-status', 'Test sync triggered');
            }
        }
        
        function triggerSettingsUpdate() {
            if (window.realtimeManager) {
                window.realtimeManager.triggerSync('settings_updated', {
                    setting: 'test_setting',
                    value: Math.random(),
                    timestamp: Date.now()
                });
                logStatus('realtime-status', 'Settings update triggered');
            }
        }
        
        function triggerAPIKeyChange() {
            if (window.realtimeManager) {
                window.realtimeManager.triggerSync('api_key_changed', {
                    provider: 'test',
                    timestamp: Date.now()
                });
                logStatus('realtime-status', 'API key change triggered');
            }
        }
        
        function checkConnectionStatus() {
            if (window.realtimeManager) {
                const status = window.realtimeManager.getConnectionStatus();
                logStatus('realtime-status', `Connection status: ${JSON.stringify(status)}`);
            }
        }
        
        // Customization Manager Tests
        async function switchTheme(themeId) {
            if (window.customizationManager) {
                try {
                    await window.customizationManager.setTheme(themeId);
                    logStatus('customization-status', `Switched to theme: ${themeId}`);
                } catch (error) {
                    logStatus('customization-status', `Error switching theme: ${error.message}`);
                }
            }
        }
        
        function openCustomizationPanel() {
            const container = document.getElementById('customization-container');
            container.style.display = 'block';
            
            if (window.CustomizationPanel) {
                const panel = new window.CustomizationPanel(container);
                panel.initialize().then(() => {
                    logStatus('customization-status', 'Customization panel opened');
                }).catch(error => {
                    logStatus('customization-status', `Error opening panel: ${error.message}`);
                });
            }
        }
        
        async function resetCustomizations() {
            if (window.customizationManager) {
                try {
                    await window.customizationManager.resetToDefaults();
                    logStatus('customization-status', 'Customizations reset to defaults');
                } catch (error) {
                    logStatus('customization-status', `Error resetting: ${error.message}`);
                }
            }
        }
        
        // Notification Manager Tests
        function showSuccessNotification() {
            if (window.notificationManager) {
                window.notificationManager.show({
                    type: 'success',
                    title: 'Test Success',
                    message: 'This is a test success notification',
                    duration: 3000
                });
                logStatus('notification-status', 'Success notification shown');
            }
        }
        
        function showErrorNotification() {
            if (window.notificationManager) {
                window.notificationManager.show({
                    type: 'error',
                    title: 'Test Error',
                    message: 'This is a test error notification',
                    duration: 5000
                });
                logStatus('notification-status', 'Error notification shown');
            }
        }
        
        function showWarningNotification() {
            if (window.notificationManager) {
                window.notificationManager.show({
                    type: 'warning',
                    title: 'Test Warning',
                    message: 'This is a test warning notification',
                    duration: 4000
                });
                logStatus('notification-status', 'Warning notification shown');
            }
        }
        
        function showRecipeNotification() {
            if (window.notificationManager) {
                window.notificationManager.show({
                    type: 'recipe',
                    title: 'Recipe Generated',
                    message: 'Delicious Pasta Carbonara has been created!',
                    duration: 6000,
                    actions: [
                        {
                            label: 'View Recipe',
                            action: () => logStatus('notification-status', 'Recipe viewed')
                        }
                    ]
                });
                logStatus('notification-status', 'Recipe notification shown');
            }
        }
        
        function clearAllNotifications() {
            if (window.notificationManager) {
                window.notificationManager.clearAll();
                logStatus('notification-status', 'All notifications cleared');
            }
        }
        
        // Demo Functions
        function simulateRecipeGeneration() {
            // Simulate a complete recipe generation workflow
            logStatus('demo-status', 'Starting recipe generation simulation...');
            
            setTimeout(() => {
                if (window.realtimeManager) {
                    window.realtimeManager.triggerSync('recipe_generated', {
                        title: 'Spicy Thai Curry',
                        ingredients: ['coconut milk', 'curry paste', 'vegetables'],
                        timestamp: Date.now()
                    });
                }
                logStatus('demo-status', 'Recipe generation completed');
            }, 1000);
        }
        
        function simulateSettingsSync() {
            logStatus('demo-status', 'Simulating settings synchronization...');
            
            setTimeout(() => {
                if (window.realtimeManager) {
                    window.realtimeManager.triggerSync('settings_updated', {
                        category: 'appearance',
                        changes: { theme: 'dark', fontSize: 16 },
                        timestamp: Date.now()
                    });
                }
                logStatus('demo-status', 'Settings synchronized');
            }, 500);
        }
        
        function simulateThemeChange() {
            const themes = ['default', 'light', 'high-contrast'];
            const randomTheme = themes[Math.floor(Math.random() * themes.length)];
            
            logStatus('demo-status', `Simulating theme change to: ${randomTheme}`);
            switchTheme(randomTheme);
        }
        
        function openNewTab() {
            window.open(window.location.href, '_blank');
            logStatus('demo-status', 'New tab opened for cross-tab testing');
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
