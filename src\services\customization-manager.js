// Customization Manager for ChefAI
class CustomizationManager {
  constructor() {
    this.themes = new Map();
    this.currentTheme = 'default';
    this.customizations = {};
    this.previewMode = false;
    this.isInitialized = false;
    
    // Default themes
    this.initializeDefaultThemes();
    
    // Customization categories
    this.CATEGORIES = {
      APPEARANCE: 'appearance',
      BEHAVIOR: 'behavior',
      INTERFACE: 'interface',
      PERFORMANCE: 'performance'
    };
  }

  // Initialize default themes
  initializeDefaultThemes() {
    // Default Dark Theme
    this.themes.set('default', {
      id: 'default',
      name: 'Default Dark',
      description: 'Professional dark theme with blue accents',
      colors: {
        primary: '#3498db',
        secondary: '#2c3e50',
        background: '#2c3e50',
        surface: '#34495e',
        accent: '#3498db',
        text: '#ecf0f1',
        textSecondary: '#bdc3c7',
        border: '#4a5f7a',
        success: '#27ae60',
        warning: '#f39c12',
        error: '#e74c3c',
        info: '#3498db'
      },
      fonts: {
        primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        monospace: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
      },
      spacing: {
        xs: '4px',
        sm: '8px',
        md: '16px',
        lg: '24px',
        xl: '32px'
      },
      borderRadius: {
        sm: '4px',
        md: '6px',
        lg: '8px',
        xl: '12px'
      },
      shadows: {
        sm: '0 2px 4px rgba(0,0,0,0.1)',
        md: '0 4px 8px rgba(0,0,0,0.15)',
        lg: '0 8px 16px rgba(0,0,0,0.2)',
        xl: '0 12px 24px rgba(0,0,0,0.25)'
      }
    });

    // Light Theme
    this.themes.set('light', {
      id: 'light',
      name: 'Clean Light',
      description: 'Clean light theme for daytime use',
      colors: {
        primary: '#2980b9',
        secondary: '#ecf0f1',
        background: '#ffffff',
        surface: '#f8f9fa',
        accent: '#3498db',
        text: '#2c3e50',
        textSecondary: '#7f8c8d',
        border: '#dee2e6',
        success: '#27ae60',
        warning: '#f39c12',
        error: '#e74c3c',
        info: '#3498db'
      },
      fonts: {
        primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        monospace: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
      },
      spacing: {
        xs: '4px',
        sm: '8px',
        md: '16px',
        lg: '24px',
        xl: '32px'
      },
      borderRadius: {
        sm: '4px',
        md: '6px',
        lg: '8px',
        xl: '12px'
      },
      shadows: {
        sm: '0 2px 4px rgba(0,0,0,0.05)',
        md: '0 4px 8px rgba(0,0,0,0.1)',
        lg: '0 8px 16px rgba(0,0,0,0.15)',
        xl: '0 12px 24px rgba(0,0,0,0.2)'
      }
    });

    // High Contrast Theme
    this.themes.set('high-contrast', {
      id: 'high-contrast',
      name: 'High Contrast',
      description: 'High contrast theme for accessibility',
      colors: {
        primary: '#ffffff',
        secondary: '#000000',
        background: '#000000',
        surface: '#1a1a1a',
        accent: '#ffff00',
        text: '#ffffff',
        textSecondary: '#cccccc',
        border: '#ffffff',
        success: '#00ff00',
        warning: '#ffff00',
        error: '#ff0000',
        info: '#00ffff'
      },
      fonts: {
        primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        monospace: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
      },
      spacing: {
        xs: '6px',
        sm: '12px',
        md: '20px',
        lg: '28px',
        xl: '36px'
      },
      borderRadius: {
        sm: '2px',
        md: '4px',
        lg: '6px',
        xl: '8px'
      },
      shadows: {
        sm: '0 2px 4px rgba(255,255,255,0.1)',
        md: '0 4px 8px rgba(255,255,255,0.15)',
        lg: '0 8px 16px rgba(255,255,255,0.2)',
        xl: '0 12px 24px rgba(255,255,255,0.25)'
      }
    });
  }

  // Initialize customization manager
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Load saved customizations
      await this.loadCustomizations();
      
      // Apply current theme
      await this.applyTheme(this.currentTheme);
      
      this.isInitialized = true;
      console.log('CustomizationManager initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize CustomizationManager:', error);
    }
  }

  // Load customizations from storage
  async loadCustomizations() {
    try {
      const result = await chrome.storage.local.get([
        'chefai_theme_settings',
        'chefai_customizations',
        'chefai_user_preferences'
      ]);

      if (result.chefai_theme_settings) {
        this.currentTheme = result.chefai_theme_settings.currentTheme || 'default';
        
        // Load custom themes
        if (result.chefai_theme_settings.customThemes) {
          result.chefai_theme_settings.customThemes.forEach(theme => {
            this.themes.set(theme.id, theme);
          });
        }
      }

      if (result.chefai_customizations) {
        this.customizations = result.chefai_customizations;
      }

    } catch (error) {
      console.error('Error loading customizations:', error);
    }
  }

  // Save customizations to storage
  async saveCustomizations() {
    try {
      const customThemes = Array.from(this.themes.values())
        .filter(theme => !['default', 'light', 'high-contrast'].includes(theme.id));

      await chrome.storage.local.set({
        chefai_theme_settings: {
          currentTheme: this.currentTheme,
          customThemes: customThemes
        },
        chefai_customizations: this.customizations
      });

      // Trigger real-time update
      if (window.realtimeManager) {
        window.realtimeManager.triggerSync('theme_changed', {
          currentTheme: this.currentTheme,
          customizations: this.customizations
        });
      }

    } catch (error) {
      console.error('Error saving customizations:', error);
    }
  }

  // Get available themes
  getThemes() {
    return Array.from(this.themes.values());
  }

  // Get current theme
  getCurrentTheme() {
    return this.themes.get(this.currentTheme);
  }

  // Set theme
  async setTheme(themeId, preview = false) {
    if (!this.themes.has(themeId)) {
      throw new Error(`Theme '${themeId}' not found`);
    }

    if (preview) {
      this.previewMode = true;
      await this.applyTheme(themeId);
    } else {
      this.previewMode = false;
      this.currentTheme = themeId;
      await this.applyTheme(themeId);
      await this.saveCustomizations();
    }
  }

  // Apply theme to DOM
  async applyTheme(themeId) {
    const theme = this.themes.get(themeId);
    if (!theme) return;

    // Create CSS variables
    const cssVariables = this.generateCSSVariables(theme);
    
    // Apply to document
    this.applyCSSVariables(cssVariables);
    
    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme.colors.primary);
    
    // Dispatch theme change event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('chefai-theme-changed', {
        detail: { theme, preview: this.previewMode }
      }));
    }
  }

  // Generate CSS variables from theme
  generateCSSVariables(theme) {
    const variables = {};
    
    // Colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      variables[`--chefai-color-${key}`] = value;
    });
    
    // Fonts
    Object.entries(theme.fonts).forEach(([key, value]) => {
      variables[`--chefai-font-${key}`] = value;
    });
    
    // Spacing
    Object.entries(theme.spacing).forEach(([key, value]) => {
      variables[`--chefai-spacing-${key}`] = value;
    });
    
    // Border radius
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      variables[`--chefai-radius-${key}`] = value;
    });
    
    // Shadows
    Object.entries(theme.shadows).forEach(([key, value]) => {
      variables[`--chefai-shadow-${key}`] = value;
    });
    
    return variables;
  }

  // Apply CSS variables to document
  applyCSSVariables(variables) {
    if (typeof document === 'undefined') return;
    
    const root = document.documentElement;
    
    Object.entries(variables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }

  // Update meta theme color
  updateMetaThemeColor(color) {
    if (typeof document === 'undefined') return;
    
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.content = color;
  }

  // Create custom theme
  createCustomTheme(baseThemeId, customizations, name, description) {
    const baseTheme = this.themes.get(baseThemeId);
    if (!baseTheme) {
      throw new Error(`Base theme '${baseThemeId}' not found`);
    }

    const customTheme = {
      id: `custom-${Date.now()}`,
      name: name || 'Custom Theme',
      description: description || 'User created theme',
      baseTheme: baseThemeId,
      ...JSON.parse(JSON.stringify(baseTheme)), // Deep clone
      ...customizations
    };

    this.themes.set(customTheme.id, customTheme);
    return customTheme;
  }

  // Get customization options
  getCustomizationOptions() {
    return {
      [this.CATEGORIES.APPEARANCE]: {
        name: 'Appearance',
        options: {
          theme: {
            type: 'select',
            label: 'Theme',
            options: this.getThemes().map(t => ({ value: t.id, label: t.name })),
            current: this.currentTheme
          },
          primaryColor: {
            type: 'color',
            label: 'Primary Color',
            current: this.getCurrentTheme()?.colors.primary || '#3498db'
          },
          fontSize: {
            type: 'range',
            label: 'Font Size',
            min: 12,
            max: 20,
            current: this.customizations.fontSize || 14
          }
        }
      },
      [this.CATEGORIES.BEHAVIOR]: {
        name: 'Behavior',
        options: {
          autoSave: {
            type: 'boolean',
            label: 'Auto-save Settings',
            current: this.customizations.autoSave !== false
          },
          animations: {
            type: 'boolean',
            label: 'Enable Animations',
            current: this.customizations.animations !== false
          }
        }
      },
      [this.CATEGORIES.INTERFACE]: {
        name: 'Interface',
        options: {
          sidebarPosition: {
            type: 'select',
            label: 'Sidebar Position',
            options: [
              { value: 'right', label: 'Right' },
              { value: 'left', label: 'Left' }
            ],
            current: this.customizations.sidebarPosition || 'right'
          },
          compactMode: {
            type: 'boolean',
            label: 'Compact Mode',
            current: this.customizations.compactMode || false
          }
        }
      }
    };
  }

  // Update customization
  async updateCustomization(category, key, value) {
    if (!this.customizations[category]) {
      this.customizations[category] = {};
    }
    
    this.customizations[category][key] = value;
    
    // Apply immediate changes
    await this.applyCustomization(category, key, value);
    
    // Save to storage
    await this.saveCustomizations();
  }

  // Apply specific customization
  async applyCustomization(category, key, value) {
    switch (key) {
      case 'theme':
        await this.setTheme(value);
        break;
      case 'primaryColor':
        await this.updateThemeColor('primary', value);
        break;
      case 'fontSize':
        this.updateFontSize(value);
        break;
      // Add more customization handlers as needed
    }
  }

  // Update theme color
  async updateThemeColor(colorKey, value) {
    const currentTheme = this.getCurrentTheme();
    if (currentTheme) {
      currentTheme.colors[colorKey] = value;
      await this.applyTheme(this.currentTheme);
    }
  }

  // Update font size
  updateFontSize(size) {
    if (typeof document !== 'undefined') {
      document.documentElement.style.setProperty('--chefai-font-size-base', `${size}px`);
    }
  }

  // Reset to defaults
  async resetToDefaults() {
    this.currentTheme = 'default';
    this.customizations = {};
    await this.applyTheme('default');
    await this.saveCustomizations();
  }

  // Export theme
  exportTheme(themeId) {
    const theme = this.themes.get(themeId);
    if (!theme) return null;
    
    return JSON.stringify(theme, null, 2);
  }

  // Import theme
  async importTheme(themeData) {
    try {
      const theme = JSON.parse(themeData);
      theme.id = `imported-${Date.now()}`;
      this.themes.set(theme.id, theme);
      await this.saveCustomizations();
      return theme;
    } catch (error) {
      throw new Error('Invalid theme data');
    }
  }
}

// Export for use in different contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CustomizationManager;
} else if (typeof window !== 'undefined') {
  window.CustomizationManager = CustomizationManager;
}

// Create global instance
const customizationManager = new CustomizationManager();

// Auto-initialize
if (typeof chrome !== 'undefined' || typeof window !== 'undefined') {
  customizationManager.initialize().catch(console.error);
}
